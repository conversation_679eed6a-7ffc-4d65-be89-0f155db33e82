import { logger } from "@/lib/logger";
import { redis } from "@/lib/redis";
import { userIdStringSchema } from "@/lib/zod";
import { Cart, CartItem } from "@/types/cart";

//Get cart count
export async function getCartCount(userIdDb: string): Promise<number>{

  if (!userIdDb){
    logger.warn(`[getCartCount] No paramater provided`)
    return 0
  }

  try{
    const userIdParsed = userIdStringSchema.safeParse(userIdDb)

    if (!userIdParsed.success) {
      logger.error("[getCartCount] Invalid user ID provided:", userIdParsed.error.format());
      return 0; 
    }

    const userId = userIdParsed.data
    const cacheKey = `cart-${userId}`;

    let cart: Cart | null | undefined = await redis?.get(cacheKey);

    const cartCount = cart?.items.reduce((sum, item) => sum + item.quantity, 0) || 0;

    if (!cartCount) {
      logger.warn(`[getCartCount] No cart found for userId ${userIdDb}`)
      return 0
    }
    return cartCount
  }catch(e){
    logger.error(`[getCartCount] Error: ${e}`)
    return 0
  }
}

export async function getCartItems(userId: string): Promise<CartItem[]> {
  if (!userId) {
    logger.warn(`[getCartItems] No userId provided`);
    return [];
  }

  try {
    const cacheKey = `cart-${userId}`;
    let cart: Cart | null | undefined = await redis?.get(cacheKey);
    
    if (!cart || !cart.items || !Array.isArray(cart.items)) {
      logger.warn(`[getCartItems] No valid cart found for userId ${userId}`);
      return [];
    }

    return cart.items;
  } catch (error) {
    logger.error(`[getCartItems] Error fetching cart: ${error}`);
    return [];
  }
}

// // //Get cart count
// export async function getCartCount(userIdDb: string){

//   if (!userIdDb){
//     logger.warn(`[getCartCount] No paramater provided`)
//     return 0
//   }

//   try{
//     const userIdParsed = userIdStringSchema.safeParse(userIdDb)

//     if (!userIdParsed.success) {
//       logger.error("[getCartCount] Invalid user ID provided:", userIdParsed.error.format());
//       return 0; 
//     }

//     const userId = userIdParsed.data
//     const cacheKey = `wishlist-count:${userId}`;

//     let cart: Cart | null | undefined = await redis?.get(`cart-${dbUser.id}`);

//     const cartCount = cart?.items.reduce((sum, item) => sum + item.quantity, 0) || 0;

//     if (!cartCount) return 0

//     return cartCount
//   }catch(e){
//     logger.error(`[getCartCount] Error: ${e}`)
//     return 0
//   }
// }

// // Get recent orders for the current user
// export const getRecentOrders = cache(async (limit = 8): Promise<ProductCardInterface[]> => {

//   const { dbUser } = await getUserData()

//   if (!dbUser) return []
    
//   const orders = await withRetry(() =>
//     prisma.order.findMany({
//       where: { userId: dbUser.id },
//       orderBy: { createdAt: 'desc' },
//       take: limit,
//       include: {
//         orderItems: {
//           include: {
//             product: {
//                 select: {
//                     id: true,
//                     Material_Number: true,
//                     Description_Local: true,
//                     PretAM: true,
//                     FinalPrice: true,
//                     ImageUrl: true,
//                     categoryLevel3: {
//                     select: { name: true }
//                     },
//                     HasDiscount: true,
//                     stockStatus: true,
//                     productClass: {
//                     select: {
//                         vehicleModels: {
//                         select: {
//                             vehicleModel: {
//                             select: {
//                                 name: true
//                             }
//                             }
//                         }
//                         }
//                     }
//                     }
//                 }
//                 },
//           },
//         },
//       },
//     })
//   );

//   if (!orders) return [];

//   // Flatten the products from all orders
//   const productMap = new Map<string, ProductCardInterface>();

//   for (const order of orders) {
//     for (const item of order.orderItems) {
//       const p = item.product;

//       if (!p) continue; // safeguard

//       if (!productMap.has(p.id)) {
//         productMap.set(p.id, {
//           id: p.id,
//           Material_Number: p.Material_Number,
//           Description_Local: p.Description_Local ?? '',
//           PretAM: Number(p.PretAM ?? 0),
//           FinalPrice: p.FinalPrice ? Number(p.FinalPrice) : undefined,
//           ImageUrl: p.ImageUrl?.[0] ?? '',
//           categoryLevel3: p.categoryLevel3?.name ?? '',
//           HasDiscount: p.HasDiscount ?? false,
//           VehicleModel: p.productClass?.vehicleModels?.map(vm => vm.vehicleModel.name) ?? [],
//           stockStatus: p.stockStatus,
//         });
//       }
//     }
//   }

//   return Array.from(productMap.values());
// });
