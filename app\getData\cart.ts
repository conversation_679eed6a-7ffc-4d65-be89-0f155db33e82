import { logger } from "@/lib/logger";
import { redis } from "@/lib/redis";
import { cuidSchema } from "@/lib/zod";
import { Cart, CartItem } from "@/types/cart";

//Get cart count
export async function getCartCount(userIdDb: string): Promise<number>{

  if (!userIdDb){
    logger.warn(`[getCartCount] No paramater provided`)
    return 0
  }

  const userIdParsed = cuidSchema.safeParse(userIdDb)

  if (!userIdParsed.success) {
    logger.error("[getCartCount] Invalid user ID provided:", userIdParsed.error.format());
    return 0; 
  }

  const userId = userIdParsed.data

  try{

    const cacheKey = `cart-${userId}`;

    let cart: Cart | null | undefined = await redis?.get(cacheKey);

    const cartCount = cart?.items.reduce((sum, item) => sum + item.quantity, 0) || 0;

    if (!cartCount) {
      logger.warn(`[getCartCount] No cart found for userId ${userIdDb}`)
      return 0
    }
    return cartCount
  }catch(e){
    logger.error(`[getCartCount] Error: ${e}`)
    return 0
  }
}

export async function getCartItems(userIdDB: string): Promise<CartItem[]> {
  if (!userIdDB) {
    logger.warn(`[getCartItems] No userId provided`);
    return [];
  }

  const userIdParsed = cuidSchema.safeParse(userIdDB);

  if (!userIdParsed.success) {
    logger.error("[getCartItems] Invalid user ID provided:", userIdParsed.error.format());
    return [];
  }

  const userId = userIdParsed.data

  try {
    const cacheKey = `cart-${userId}`;
    let cart: Cart | null | undefined = await redis?.get(cacheKey);

    //console.log(`[getCartItems] Raw cart data for ${userId}:`, cart);

    if (!cart || !cart.items || !Array.isArray(cart.items)) {
      logger.warn(`[getCartItems] No valid cart found for userId ${userId}`);
      return [];
    }

    //console.log(`[getCartItems] Returning ${cart.items.length} items for ${userId}`);
    return cart.items;
  } catch (error) {
    logger.error(`[getCartItems] Error fetching cart: ${error}`);
    return [];
  }
}
