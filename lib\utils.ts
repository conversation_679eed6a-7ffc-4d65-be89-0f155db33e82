import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function getClientIp(req: Request): string | null {
  const forwarded = req.headers.get('x-forwarded-for');
  if (forwarded) return forwarded.split(',')[0].trim();
  return req.headers.get('x-real-ip');
}

export  function getStockStatus(stock?: number) {
      if (stock === undefined) return "UNKNOWN";
      if (stock > 5) return "IN_STOCK";
      if (stock > 0) return "LOW_STOCK";
      return "OUT_OF_STOCK";
}

export function formatPriceRON(price?: number): string {
  if (typeof price !== "number") return "";
  return new Intl.NumberFormat("ro-RO", {
    style: "currency",
    currency: "RON",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(price);
}

export const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));
