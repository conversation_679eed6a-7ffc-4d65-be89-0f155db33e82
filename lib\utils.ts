import { Decimal } from "@/generated/prisma/runtime/library";
import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function getClientIp(req: Request): string | null {
  const forwarded = req.headers.get('x-forwarded-for');
  if (forwarded) return forwarded.split(',')[0].trim();
  return req.headers.get('x-real-ip');
}

export  function getStockStatus(stock?: number) {
      if (stock === undefined) return "UNKNOWN";
      if (stock > 5) return "in stoc";
      if (stock > 0) return "stoc mic";
      return "stoc epuizat";
}

export function formatPriceRON(price: number | null): string {
  if (typeof price !== "number") return "";

  const formatted = new Intl.NumberFormat("ro-RO", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(price);

  return `${formatted} Lei`; // Capitalized
}

export function formatDiscount(
  type: "PERCENTAGE" | "FIXED_AMOUNT" | "NEW_PRICE" | null,
  value: number | null
): string | null {
  if (type === null || value === null) return null;

  const formatted = value.toLocaleString("ro-RO", { minimumFractionDigits: 2 });

  switch (type) {
    case "PERCENTAGE":
      return `-${formatted}%`;
    case "FIXED_AMOUNT":
      return `-${formatted} RON`;
    case "NEW_PRICE":
      return `${formatted} RON`;
    default:
      return null;
  }
}

export function convertDecimalFields<T extends Record<string, any>>(obj: T): T {
  return Object.fromEntries(
    Object.entries(obj).map(([key, value]) => [
      key,
      value instanceof Decimal ? value.toNumber() : value,
    ])
  ) as T;
}


// Recursively walks and converts Decimal to number
export function sanitizePrismaObject<T>(obj: T): T {
  if (obj instanceof Decimal) {
    return obj.toNumber() as T;
  }

  if (Array.isArray(obj)) {
    return obj.map(sanitizePrismaObject) as T;
  }

  if (typeof obj === "object" && obj !== null) {
    const result: any = {};
    for (const [key, value] of Object.entries(obj)) {
      result[key] = sanitizePrismaObject(value);
    }
    return result;
  }

  return obj;
}





export const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));
