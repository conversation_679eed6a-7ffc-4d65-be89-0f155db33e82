"use client"


import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Heart, Home, MapPin } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
// import RelatedProductGrid from "@/app/components/storefront/RelatedProductGrid";
import Link from "next/link";
import { Input } from "@/components/ui/input";
import Image from "next/image";
import { formatPriceRON } from "@/lib/utils";
import { ProductPage } from "@/types/product";

export default function ProductPageContent({ product, stock }: { product: ProductPage, stock: number }) {

  const [quantity, setQuantity] = useState(1);
  const [isStockDialogOpen, setIsStockDialogOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState(0);

  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (!isNaN(value) && value > 0) {
      setQuantity(value);
    }
  };

  const handleIncrement = () => {
    setQuantity((prev) => prev + 1);
  };

  const handleDecrement = () => {
    if (quantity > 1) {
      setQuantity((prev) => prev - 1);
    }
  };

      return (<>
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Breadcrumbs */}
      <nav className="text-sm mb-8">
        <ol className="flex items-center space-x-2">
          <li>
            <Link href="/" className="text-gray-500 hover:text-gray-700">
              <Home />
            </Link>
          </li>
          <li>
            <span className="text-gray-400">/</span>
          </li>
          <li>
            <Link href={`/${product.categoryLevel3?.name}`} className="text-gray-500 hover:text-gray-700">
              {product.categoryLevel3?.name}
            </Link>
          </li>
          <li>
            <span className="text-gray-400">/</span>
          </li>
          <li className="text-gray-900">{product.Description_Local}</li>
        </ol>
      </nav>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {/* Product Image */}
        <div className="space-y-4">
          <div className="aspect-w-4 aspect-h-3 bg-gray-100 rounded-lg overflow-hidden">
            <Image
              src={product.ImageUrl[selectedImage]}
              alt="KW V3 Coilover Kit"
              className="w-full h-full object-cover"
              width={500}
              height={500}
            />
          </div>

          {/* Thumbnails */}
          <div className="flex gap-2 overflow-x-auto">
            {product.ImageUrl.map((img, index) => (
              <Button
                key={index}
                className={`w-20 h-20 rounded-md overflow-hidden border-2 ${selectedImage === index ? "border-[#0066B1]" : "border-transparent"}`}
                onClick={() => setSelectedImage(index)}
              >
                <Image
                  src={img}
                  alt={`Thumbnail ${index + 1}`}
                  className="w-full h-full object-cover"
                  width={200}
                  height={200}
                />
              </Button>
            ))}
          </div>
        </div>

        {/* Product Info */}
        <div className="space-y-6">

          {/* Title and Code */}
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {product.Description_Local}
            </h1> 
            <p className="text-lg text-gray-500 mt-2">
              OE: {product.Material_Number}
            </p> 
          </div>
          
          {/* Price */}
          <div className="flex items-baseline">
            <span className="text-3xl font-bold text-gray-900">
              {formatPriceRON(product.FinalPrice)}
            </span>
          </div>

          {/* Stock Status */}
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm font-medium">In Stock: 10 units</span>
            </div>
            <Button
              variant="link"
              className="text-[#0066B1] hover:text-[#004d85] p-0 h-auto font-medium"
              onClick={() => setIsStockDialogOpen(true)}
            >
              See Stock
            </Button>
          </div>

          {/* Add to Cart Section */}
          <div className="pt-6 border-t border-gray-200">
            <div className="flex items-center gap-4">
            <div className="flex items-center">
                <button
                  onClick={handleDecrement}
                  className="w-10 h-10 flex items-center justify-center rounded-l-md bg-gray-100 hover:bg-gray-200 transition-colors"
                >
                  −
                </button>
                <input
                  type="text"
                  value={quantity}
                  onChange={handleQuantityChange}
                  className="w-12 h-10 text-center border-y border-gray-200 focus:outline-none focus:ring-0"
                  min="1"
                />
                <button
                  onClick={handleIncrement}
                  className="w-10 h-10 flex items-center justify-center rounded-r-md bg-gray-100 hover:bg-gray-200 transition-colors"
                >
                  +
                </button>
              </div>

              <Button className="flex-1 bg-[#0066B1] hover:bg-[#004d85] h-10">
                Add to Cart
              </Button>

              <Button
                variant="outline"
                size="icon"
                className="h-10 w-10 rounded-full border-[#0066B1] text-[#0066B1] hover:bg-[#0066B1] hover:text-white"
              >
                <Heart className="w-5 h-5" />
              </Button>
            </div>
          </div>
        </div>

        {/* Product Information */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Product Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex justify-between items-center py-2 border-b border-gray-200">
              <span className="text-sm font-medium text-gray-600">
                Has Discount
              </span>
              <span className="text-sm text-gray-900">
                <span
                  className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    true
                      ? "bg-green-100 text-green-800"
                      : "bg-red-100 text-red-800"
                  }`}
                >
                  {true ? "Yes" : "No"}
                </span>
              </span>
            </div>
            <div className="flex justify-between items-center py-2 border-b border-gray-200">
              <span className="text-sm font-medium text-gray-600">
                Active Discount Value
              </span>
              <span className="text-sm text-gray-900 font-semibold">
                15%
              </span>
            </div>
            <div className="flex justify-between items-center py-2 border-b border-gray-200">
              <span className="text-sm font-medium text-gray-600">
                Net Weight
              </span>
              <span className="text-sm text-gray-900">12.5 kg</span>
            </div>
            <div className="flex justify-between items-center py-2 border-b border-gray-200">
              <span className="text-sm font-medium text-gray-600">
                Base Unit of Measure
              </span>
              <span className="text-sm text-gray-900">EA</span>
            </div>
            <div className="flex justify-between items-center py-2 border-b border-gray-200">
              <span className="text-sm font-medium text-gray-600">
                Cross Plant
              </span>
              <span className="text-sm text-gray-900">
                <span
                  className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    true
                      ? "bg-blue-100 text-blue-800"
                      : "bg-gray-100 text-gray-800"
                  }`}
                >
                  {true ? "Available" : "Not Available"}
                </span>
              </span>
            </div>
            <div className="flex justify-between items-center py-2 border-b border-gray-200">
              <span className="text-sm font-medium text-gray-600">
                New Material
              </span>
              <span className="text-sm text-gray-900">
                <span
                  className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    false
                      ? "bg-green-100 text-green-800"
                      : "bg-yellow-100 text-yellow-800"
                  }`}
                >
                  {false ? "New" : "Existing"}
                </span>
              </span>
            </div>
          </div>
        </div>

                  {/* Product Tabs */}
          <Tabs defaultValue="models" className="w-full mt-6">
            <TabsList className="grid grid-cols-3 mb-4">
              <TabsTrigger value="models">Models</TabsTrigger>
              {/* <TabsTrigger value="submodels">Submodels</TabsTrigger> */}
              <TabsTrigger value="engines">Engines</TabsTrigger>
            </TabsList>

            <TabsContent value="models" className="p-4 border rounded-md">
              <h3 className="font-medium mb-2">Compatible BMW Models</h3>
              <div className="grid grid-cols-5 gap-2">
                {product.productClass?.vehicleModels.map((model, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-[#0066B1] rounded-full"></div>
                    <span>{model.vehicleModel.name}</span>
                  </div>
                ))}
              </div>
            </TabsContent>

            {/* <TabsContent value="submodels" className="p-4 border rounded-md">
              <h3 className="font-medium mb-2">Compatible Submodels</h3>
              <div className="grid grid-cols-5 gap-2">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-[#0066B1] rounded-full"></div>
                  <span>F30</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-[#0066B1] rounded-full"></div>
                  <span>F32</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-[#0066B1] rounded-full"></div>
                  <span>F10</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-[#0066B1] rounded-full"></div>
                  <span>G20</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-[#0066B1] rounded-full"></div>
                  <span>G22</span>
                </div>
              </div>
            </TabsContent> */}
            <TabsContent value="engines" className="p-4 border rounded-md">
              <h3 className="font-medium mb-2">Compatible Engines</h3>
              <div className="grid grid-cols-5 gap-2">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-[#0066B1] rounded-full"></div>
                  <span>N47</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-[#0066B1] rounded-full"></div>
                  <span>B47</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-[#0066B1] rounded-full"></div>
                  <span>N57</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-[#0066B1] rounded-full"></div>
                  <span>S55</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-[#0066B1] rounded-full"></div>
                  <span>S58</span>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        
        {/* Product Attributes */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Product Attributes
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div className="flex justify-between items-center py-2 px-3 bg-white rounded border">
              <span className="text-sm font-medium text-gray-600">
                Material Type
              </span>
              <span className="text-sm text-gray-900">Steel Alloy</span>
            </div>
            <div className="flex justify-between items-center py-2 px-3 bg-white rounded border">
              <span className="text-sm font-medium text-gray-600">
                Finish
              </span>
              <span className="text-sm text-gray-900">Powder Coated</span>
            </div>
            <div className="flex justify-between items-center py-2 px-3 bg-white rounded border">
              <span className="text-sm font-medium text-gray-600">
                Adjustability
              </span>
              <span className="text-sm text-gray-900">32-Way Damping</span>
            </div>
            <div className="flex justify-between items-center py-2 px-3 bg-white rounded border">
              <span className="text-sm font-medium text-gray-600">
                Spring Rate
              </span>
              <span className="text-sm text-gray-900">Variable</span>
            </div>
            <div className="flex justify-between items-center py-2 px-3 bg-white rounded border">
              <span className="text-sm font-medium text-gray-600">
                Warranty
              </span>
              <span className="text-sm text-gray-900">2 Years</span>
            </div>
            <div className="flex justify-between items-center py-2 px-3 bg-white rounded border">
              <span className="text-sm font-medium text-gray-600">
                Origin
              </span>
              <span className="text-sm text-gray-900">Germany</span>
            </div>
            <div className="flex justify-between items-center py-2 px-3 bg-white rounded border">
              <span className="text-sm font-medium text-gray-600">
                Installation Time
              </span>
              <span className="text-sm text-gray-900">4-6 Hours</span>
            </div>
            <div className="flex justify-between items-center py-2 px-3 bg-white rounded border">
              <span className="text-sm font-medium text-gray-600">
                Performance Level
              </span>
              <span className="text-sm text-gray-900">Track/Street</span>
            </div>
          </div>
        </div>
      </div>

      {/* Stock Location Dialog */}
      {/* <Dialog open={isStockDialogOpen} onOpenChange={setIsStockDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Stock Availability</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {stockLocations.map((location, index) => (
              <div
                key={index}
                className="flex items-start gap-4 p-4 rounded-lg border border-gray-200"
              >
                <div className="p-2 rounded-full bg-gray-100">
                  <MapPin className="w-5 h-5 text-gray-600" />
                </div>
                <div className="flex-1">
                  <h3 className="font-medium">{location.name}</h3>
                  <p className="text-sm text-gray-500">{location.address}</p>
                  <p
                    className={`text-sm mt-1 ${location.stock > 0 ? "text-green-600" : "text-red-600"}`}
                  >
                    {location.stock > 0
                      ? `${location.stock} in stock`
                      : "Out of stock"}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </DialogContent>
      </Dialog> */}

      {/* <RelatedProductGrid /> */}
    </div>
  </>);
}