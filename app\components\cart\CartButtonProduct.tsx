// "use client";

// import { Button } from "@/components/ui/button";
// import { ShoppingCart } from "lucide-react";
// import { addItemToCart } from "@/app/actions/cart";
// import { toast } from "sonner";
// import { useCart } from "@/app/context/CartContext";
// import { useTransition, useRef, useOptimistic } from "react";

// export default function CartButtonProduct({ product }: { product: string }) {
//   const { cartCount, setCartCount } = useCart();
//   const [optimisticCount, addOptimistic] = useOptimistic(
//     cartCount,
//     (prevCount, update: number) => prevCount + update
//   );
//   const [isPending, startTransition] = useTransition();
//   const debounceTimer = useRef<NodeJS.Timeout | null>(null);

//   const handleAddToCart = () => {
//     if (debounceTimer.current) clearTimeout(debounceTimer.current);

//     debounceTimer.current = setTimeout(() => {
//       // Optimistically update the UI before calling server
      

//       startTransition(async () => {
//         try {
//           addOptimistic(1); // increase count by 1
//           const response = await addItemToCart(product);
//           if (response.success) {
//             setCartCount((prev) => prev + 1); // Confirm the count
//             toast.success("Adăugat cu succes în coș.");
//           } else {
//             toast.error("Nu s-a putut adăuga în coș.");
//             setCartCount((prev) => Math.max(0, prev)); // rollback manually if needed
//           }
//         } catch {
//           toast.error("Eroare la actualizarea coșului.");
//           setCartCount((prev) => Math.max(0, prev)); // rollback
//         }
//       });
//     }, 150);
//   };

//   return (
//     <Button
//       onClick={handleAddToCart}
//       className="p-2 rounded-full bg-[#0066B1] hover:bg-[#004d85] text-white shadow-sm transition-colors"
//       // Optionally show spinner or disable during transition
//       // disabled={isPending}
//     >
//       <ShoppingCart className="w-5 h-5" />
//     </Button>
//   );
// }





"use client";

import { Button } from "@/components/ui/button";
import { ShoppingCart } from "lucide-react";
import { addItemToCart } from "@/app/actions/cart";
import { toast } from "sonner";
import { useCart } from "@/app/context/CartContext";
import { useTransition, useRef } from "react";

export default function CartButtonProduct({ product }: { product: string }) {
  const { setCartCount } = useCart();
  const [isPending, startTransition] = useTransition();
  const debounceTimer = useRef<NodeJS.Timeout | null>(null);

  const handleAddToCart = () => {
    if (debounceTimer.current) clearTimeout(debounceTimer.current);

    debounceTimer.current = setTimeout(() => {
      startTransition(async () => {
        try {
          const response = await addItemToCart(product);
          if (response.success) {
            setCartCount((prev) => Math.max(0, prev + 1));
            toast.success("Adăugat cu succes în coș.");
          } else {
            toast.error("Nu s-a putut adăuga în coș.");
          }
        } catch {
          toast.error("Eroare la actualizarea coșului.");
        }
      });
    }, 150);
  };

  return (
    <Button
      onClick={handleAddToCart}
      className="p-2 rounded-full bg-[#0066B1] hover:bg-[#004d85] text-white shadow-sm transition-colors"
      //disabled={isPending}
    >
      <ShoppingCart className="w-5 h-5" />
    </Button>
  );
}
