// "use client";

// import { Button } from "@/components/ui/button";
// import { ShoppingCart } from "lucide-react";
// import { addItemToCart } from "@/app/actions/cart";
// import { toast } from "sonner";
// import { useCart } from "@/app/context/CartContext";
// import { useTransition, useRef, useOptimistic } from "react";

// export default function CartButtonProduct({ product }: { product: string }) {
//   const { cartCount, setCartCount } = useCart();
//   const [optimisticCount, addOptimistic] = useOptimistic(
//     cartCount,
//     (prevCount, update: number) => prevCount + update
//   );
//   const [isPending, startTransition] = useTransition();
//   const debounceTimer = useRef<NodeJS.Timeout | null>(null);

//   const handleAddToCart = () => {
//     if (debounceTimer.current) clearTimeout(debounceTimer.current);

//     debounceTimer.current = setTimeout(() => {
//       // Optimistically update the UI before calling server
      

//       startTransition(async () => {
//         try {
//           addOptimistic(1); // increase count by 1
//           const response = await addItemToCart(product);
//           if (response.success) {
//             setCartCount((prev) => prev + 1); // Confirm the count
//             toast.success("Adăugat cu succes în coș.");
//           } else {
//             toast.error("Nu s-a putut adăuga în coș.");
//             setCartCount((prev) => Math.max(0, prev)); // rollback manually if needed
//           }
//         } catch {
//           toast.error("Eroare la actualizarea coșului.");
//           setCartCount((prev) => Math.max(0, prev)); // rollback
//         }
//       });
//     }, 150);
//   };

//   return (
//     <Button
//       onClick={handleAddToCart}
//       className="p-2 rounded-full bg-[#0066B1] hover:bg-[#004d85] text-white shadow-sm transition-colors"
//       // Optionally show spinner or disable during transition
//       // disabled={isPending}
//     >
//       <ShoppingCart className="w-5 h-5" />
//     </Button>
//   );
// }





"use client";

import { Button } from "@/components/ui/button";
import { Loader2, ShoppingCart } from "lucide-react";
import { addItemToCart } from "@/app/actions/cart";
import { toast } from "sonner";
import { useCart } from "@/app/context/CartContext";
import { useTransition, useRef } from "react";

export default function CartButtonProduct({ product }: { product: string }) {
  const [isPending, startTransition] = useTransition();
  const { updateActualCart, cartItems } = useCart();

  const handleAddToCart = async () => {
      startTransition(async () => {
        try {
          const response = await addItemToCart(product);
          if (response.success && response.cartItem) {
            // Update the actual cart context with the new item
            const cartItem = response.cartItem;
            const updatedCartItems = [...cartItems];
            const existingItemIndex = updatedCartItems.findIndex(item => item.id === cartItem.id);

            if (existingItemIndex >= 0) {
              // Update existing item quantity
              updatedCartItems[existingItemIndex] = {
                ...updatedCartItems[existingItemIndex],
                quantity: updatedCartItems[existingItemIndex].quantity + 1
              };
            } else {
              // Add new item to cart
              updatedCartItems.push(cartItem);
            }

            // Update the actual cart state (this will update the navbar count)
            updateActualCart(updatedCartItems);
            toast.success("Adăugat cu succes în coș.");
          } else {
            toast.error("Nu s-a putut adăuga în coș.");
          }
        } catch (error) {
          toast.error("Eroare la actualizarea coșului.");
          console.error('Add to cart failed:', error);
        }
      });
    }

    return (
    <>
      {isPending ? (
        <Button 
          disabled 
          size="icon" 
          className="p-2 rounded-full bg-[#0066B1] hover:bg-[#004d85] text-white shadow-sm transition-colors"
        >
          <div className="absolute inset-0 animate-pulse-scale" />
          <Loader2 className="h-5 w-5 animate-spin" /> 
        </Button>
      ) : (
        <Button 
          onClick={handleAddToCart}
          size="icon" 
          className="p-2 rounded-full bg-[#0066B1] hover:bg-[#004d85] text-white shadow-sm transition-colors"
        >
          <ShoppingCart className="w-5 h-5" />
        </Button>
      )}
    </>
  );

  // return (
  //   <Button
  //     onClick={handleAddToCart}
  //     className="p-2 rounded-full bg-[#0066B1] hover:bg-[#004d85] text-white shadow-sm transition-colors"
  //     //disabled={isPending}
  //   >
  //     {/* <ShoppingCart 
  //       className={cn(
  //         "w-5 h-5 transition-colors",
  //         isPending && "animate-spin"
  //       )}
  //     /> */}
  //     <ShoppingCart 
  //       className={cn(
  //         "w-5 h-5 transition-colors",
  //         //isPending && "animate-spin [animation-duration:0.5s] [animation-timing-function:linear]"
  //         isPending && <Circle className="w-5 h-5 animate-spin" />
  //       )}
  //     />
  //   </Button>
  // );
}
