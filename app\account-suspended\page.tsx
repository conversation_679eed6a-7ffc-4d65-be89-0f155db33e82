import { SignOutButton } from "@clerk/nextjs";
import { <PERSON><PERSON> } from "@/components/ui/button";

export default function AccountSuspendedPage() {
  return (
    <div className="container mx-auto py-16 text-center">
      <div className="max-w-md mx-auto p-8 rounded-lg shadow-md">
        <h1 className="text-2xl font-bold text-red-600 mb-4">Account Suspended</h1>
        
        <p className="mb-6 text-gray-700">
          Your account has been suspended. This may be due to a violation of our terms of service
          or suspicious activity detected on your account.
        </p>
        
        <p className="mb-8 text-gray-700">
          If you believe this is an error, please contact our support team at 
          <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
            <EMAIL>
          </a>
        </p>
        
        <SignOutButton>
          <Button variant="outline" className="w-full">
            Sign Out
          </Button>
        </SignOutButton>
      </div>
    </div>
  );
}