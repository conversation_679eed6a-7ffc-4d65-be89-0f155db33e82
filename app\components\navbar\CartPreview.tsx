import { ShoppingCart } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { CartItem } from "@/types/cart";
import { formatPriceRON } from "@/lib/utils";

const CartPreview = ({ items }: { items: CartItem[]}) => {
  const subtotal = items.reduce(
    (acc, item) => acc + item.FinalPrice * item.quantity,
    0,
  );

  if (items.length === 0){
    return (
     <div className="absolute right-0 top-full mt-1 w-64 z-50 border border-gray-100 dark:border-gray-700 rounded-lg shadow-xl
        bg-white dark:bg-gray-800 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-medium text-gray-900 dark:text-gray-100">Cos</h3>
            <Badge variant="secondary">0 produse</Badge>
          </div>
          <p className="text-sm text-gray-500 dark:text-gray-400">Cosul tau este gol, incearca sa adaugi ceva.</p>
        </div>
      </div>
    )
  };

  return (
    <div className="absolute right-0 top-full mt-1 w-80 z-50 border border-gray-100 dark:border-gray-700 rounded-lg shadow-xl
      bg-white dark:bg-gray-800 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-medium text-gray-900 dark:text-gray-100">Cos</h3>
          <Badge variant="secondary">{items.length} produse</Badge>
        </div>
        
        <div className="space-y-3 max-h-60 overflow-auto">
          {items.map((item) => (
            <div key={item.id} className="flex gap-3 pb-3 border-b border-gray-100 dark:border-gray-700">
              <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-md overflow-hidden flex-shrink-0">
                {/* Image content */}
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">{item.Description_Local}</h4>
                <p className="text-xs text-gray-500 dark:text-gray-400">{item.quantity} x {formatPriceRON(item.FinalPrice)}</p>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-4 pt-3 border-t border-gray-100 dark:border-gray-700">
          <div className="flex justify-between mb-4">
            <span className="font-medium text-gray-900 dark:text-gray-100">Subtotal:</span>
            <span className="font-medium text-gray-900 dark:text-gray-100">{formatPriceRON(subtotal)}</span>
          </div>
          
          <div className="space-y-2">
            <Link href="/cart">
              {/* <Button variant="outline" className="w-full bg-white dark:bg-gray-700 border-gray-200 dark:border-gray-600 text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-600">
                Vezi cos
              </Button> */}
            <Button className="w-full bg-[#0066B1] hover:bg-[#004d85] text-white">
              Vezi cos
            </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CartPreview;
