"use server"

import { getCurrentDbUser } from "@/lib/auth";
import { prisma, withRetry } from "@/lib/db";
import { logger } from "@/lib/logger";
import { redis } from "@/lib/redis";
import { cuidSchema, productCodSchema } from "@/lib/zod";
import { Cart, CartItem } from "@/types/cart";
import { revalidatePath } from "next/cache";

export async function addItemToCart(product: string) {
  try {

    const user = await getCurrentDbUser();
    if (!user) {
        logger.error(`[addItemToCart] No user authenticated`);
        return { success: false };
    }

    const validatedProduct = productCodSchema.parse(product);

    // Retrieve the cart from Redis
    let cart: Cart | null | undefined = await redis?.get(`cart-${user.id}`);

    // Fetch product details
    const selectedProduct = await withRetry(() =>
      prisma.product.findUnique({
      select: {
        id: true,
        Material_Number: true,
        Description_Local: true,
        ImageUrl: true,
        FinalPrice: true,
      },
      where: { Material_Number: validatedProduct },
    }));

    if (!selectedProduct) {
      logger.error(`[addItemToCart] Product not found: ${validatedProduct}`);
      return { success: false};
    }

    // Initialize the cart if it doesn't exist
    if (!cart) {
      cart = { items: [] };
    }

    // Check if the product is already in the cart
    const existingItem = cart.items.find((item) => item.Material_Number === validatedProduct);

    if (existingItem) {
      existingItem.quantity += 1;
    } else {
      cart.items.push({
        id: selectedProduct.id,
        Material_Number: selectedProduct.Material_Number,
        ImageUrl: selectedProduct.ImageUrl[0],
        Description_Local: selectedProduct.Description_Local ?? "",
        FinalPrice: selectedProduct.FinalPrice?.toNumber() ?? 0, 
        quantity: 1,
        vinNotes: "",
        addVinNotesToInvoice: false,
        addToOrder: true
      });
    }

    // Save the updated cart to Redis
    if (!redis) {
      logger.error("[addItemToCart] Redis connection not available");
      return { success: false, error: "Redis connection not available" };
    }

    const saveResult = await redis.set(`cart-${user.id}`, cart);
    console.log("Redis save result:", saveResult);

    // Revalidate paths as needed
    revalidatePath("/", "layout");
    logger.info(`[addItemToCart] Added item to cart: ${user.id} - ${validatedProduct}`);

    // Return the cart item that was added/updated
    const addedItem = cart.items.find(item => item.Material_Number === validatedProduct);
    return { success: true, cartItem: addedItem };
  } catch (error) {
    logger.error(`[addItemToCart] Error adding item to cart: ${error}`);
    return { success: false };
  }
}

export async function updateCartActiondata(data: {
  itemId: string;
  vinNotes?: string;
  addVinNotesToInvoice?: boolean;
  addToOrder?: boolean;
  quantity?: number;
}) {
  try {
    const user = await getCurrentDbUser()

    if (!user) {
        logger.error(`[updateCartActiondata] No user authenticated`);
        return { success: false };
    }

    const { itemId, vinNotes, addVinNotesToInvoice, addToOrder, quantity } = data; //quantity is optional, if not provided, it will not be updated

    const validatedProductId = cuidSchema.parse(itemId);

    // Fetch the existing cart for the user
    let cartData = await redis?.get<Cart>(`cart-${user.id}`);
    let cart: Cart = cartData ? cartData : { items: [] };

    // Find the item to update
    const existingItemIndex = cart.items.findIndex(item => item.id === validatedProductId);
    if (existingItemIndex >= 0) {
      // Update the existing item
      cart.items[existingItemIndex] = {
        ...cart.items[existingItemIndex],
        vinNotes: vinNotes ?? cart.items[existingItemIndex].vinNotes,
        addVinNotesToInvoice: addVinNotesToInvoice ?? cart.items[existingItemIndex].addVinNotesToInvoice,
        addToOrder: addToOrder ?? cart.items[existingItemIndex].addToOrder,
        quantity: quantity ?? cart.items[existingItemIndex].quantity, 
      };
      //if quantity is equal to 0 then remove the item from redis
      if (quantity === 0) cart.items.splice(existingItemIndex, 1); //remove the item from redis if quantity is 0
    }

    // Save the updated cart to Redis
    await redis?.set(`cart-${user.id}`, cart);

    // Revalidate paths as needed
    revalidatePath("/", "layout");

    return { success: true };
  }catch (error) {
    logger.error("[updateCartActiondata] Error updating cart:", error);
    return { success: false, error: "Failed to update cart. Please try again." };
  }
}

// export async function decreaseItemFromCart(formData: FormData) {
//   try {
    
//     const productId = Number(formData.get("productId"));
//     const validatedProductId = productIdSchema.parse(productId);

//     // Retrieve the cart from Redis
//     let cart = await redis.get<Cart>(`cart-${cartId}`);

//     if (!cart) {
//       throw new Error("Cart is empty.");
//     }

//     // Find the item in the cart
//     const existingItem = cart.items.find((item) => item.id === validatedProductId);

//     if (!existingItem) {
//       throw new Error("Item not found in cart.");
//     }

//     if (existingItem.quantity > 1) {
//       // Decrease the quantity if it's greater than 1
//       existingItem.quantity -= 1;
//     } else {
//       // Remove the item from the cart if the quantity is 1
//       cart.items = cart.items.filter((item) => item.id !== validatedProductId);
//     }

//     // Update the cart in Redis
//     await redis.set(`cart-${cartId}`, cart);

//     // Revalidate paths as needed
//     revalidatePath("/", "layout");

//     return { success: true };
//   } catch (error) {
//     console.error("Error decreasing item from cart:", error);
//     return { success: false, error: error || "Failed to update cart. Please try again." };
//   }
// }

// export async function deleteItemFromCart(formData: FormData) {
//   try {
//     const { getUser } = getKindeServerSession();
//     const user = await getUser();

//     // Determine cart ID: Use user ID for logged-in users, or guest_cart_token for guests
//     let cartId: string | undefined;
//     const cookieStore = cookies();

//     if (user) {
//       cartId = user.id;
//     } else {
//       // Retrieve guest_cart_token from cookies
//       cartId = cookieStore.get("guest_cart_token")?.value;

//       if (!cartId) {
//         throw new Error("Guest cart token not found. Unable to process request.");
//       }
//     }

//     // Validate and parse productId
//     const productId = Number(formData.get("productId"));
//     const validatedProductId = productIdSchema.parse(productId);

//     // Retrieve the cart from Redis
//     let cart = await redis.get<Cart>(`cart-${cartId}`);

//     if (!cart || !cart.items.length) {
//       throw new Error("Cart is empty.");
//     }

//     // Filter out the item to delete
//     cart.items = cart.items.filter((item) => item.id !== validatedProductId);

//     // Update the cart in Redis
//     await redis.set(`cart-${cartId}`, cart);

//     // Revalidate paths as needed
//     revalidatePath("/bag");

//     return { success: true };
//   } catch (error) {
//     console.error("Error deleting item from cart:", error);
//     return { success: false, error: error || "Failed to delete item from the cart. Please try again." };
//   }
// }