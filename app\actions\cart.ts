"use server"

import { getCurrentDbUser } from "@/lib/auth";
import { prisma, withRetry } from "@/lib/db";
import { logger } from "@/lib/logger";
import { redis } from "@/lib/redis";
import { delay } from "@/lib/utils";
import { productCodSchema } from "@/lib/zod";
import { Cart } from "@/types/cart";
import { revalidatePath } from "next/cache";

export async function addItemToCart(product: string) {
  try {
await delay(9000); // ⏱ Waits 9 seconds

  console.log("Delayed for 9 seconds");
    const user = await getCurrentDbUser();
    if (!user) {
        logger.error(`[addItemToCart] No user authenticated`);
        return { success: false };
    }

    const validatedProduct = productCodSchema.parse(product);

    // Retrieve the cart from Redis
    let cart: Cart | null | undefined = await redis?.get(`cart-${user.id}`);

    // Fetch product details
    const selectedProduct = await withRetry(() =>
      prisma.product.findUnique({
      select: {
        id: true,
        Material_Number: true,
        Description_Local: true,
        ImageUrl: true,
        FinalPrice: true,
      },
      where: { Material_Number: validatedProduct },
    }));

    if (!selectedProduct) {
      logger.error("product not found");
      return { success: false};
    }

    // Initialize the cart if it doesn't exist
    if (!cart) {
      logger.warn("cart not found");
      cart = { items: [] };
    }

    // Check if the product is already in the cart
    const existingItem = cart.items.find((item) => item.Material_Number === validatedProduct);

    if (existingItem) {
      existingItem.quantity += 1;
    } else {
      cart.items.push({
        id: selectedProduct.id,
        Material_Number: selectedProduct.Material_Number,
        ImageUrl: selectedProduct.ImageUrl[0],
        Description_Local: selectedProduct.Description_Local ?? "",
        FinalPrice: selectedProduct.FinalPrice?.toNumber() ?? 0, 
        quantity: 1,
        vinNotes: "",
        addVinNotesToInvoice: false,
        addToOrder: true
      });
    }

    // Save the updated cart to Redis
    await redis?.set(`cart-${user.id}`, cart);
console.log("finished")
    // Revalidate paths as needed
    revalidatePath("/", "layout");
    logger.info(`[addItemToCart] Added item to cart: ${user.id} - ${validatedProduct}`);
    return { success: true };
  } catch (error) {
    logger.error("Error adding item to cart:", error);
    return { success: false, error: "A aparut o eroare la adaugarea produsului in cos." };
  }
}

// export async function updateCartActiondata(data: {
//   itemId: number;
//   vinNotes?: string;
//   addToInvoice?: boolean;
//   addToOrder?: boolean;
// }) {
//   try {
//     const { getUser } = getKindeServerSession();
//     const user = await getUser();
//     const { itemId, vinNotes, addToInvoice, addToOrder } = data;

//     const validatedProductId = productIdSchema.parse(itemId);

//     // Determine cart ID: User ID for logged-in users, guest_cart_token for guests
//     let cartId: string | undefined;
//     const cookieStore = cookies();
    
//     if (user) {
//       cartId = user.id;
//     } else {
//       cartId = cookieStore.get("guest_cart_token")?.value;
//     }

//     // Fetch the existing cart for the user
//     let cartData = await redis.get<CartWithObs>(`cart-${cartId}`);
//     let cart: CartWithObs = cartData ? cartData : { items: [] };

//     // Find the item to update
//     const existingItemIndex = cart.items.findIndex(item => item.id === validatedProductId);
//     if (existingItemIndex >= 0) {
//       // Update the existing item
//       cart.items[existingItemIndex] = {
//         ...cart.items[existingItemIndex],
//         vinNotes: vinNotes ?? cart.items[existingItemIndex].vinNotes,
//         addToInvoice: addToInvoice ?? cart.items[existingItemIndex].addToInvoice,
//         addToOrder: addToOrder ?? cart.items[existingItemIndex].addToOrder,
//       };
//     }

//     // Save the updated cart to Redis
//     await redis.set(`cart-${cartId}`, cart);

//     // Revalidate paths as needed
//     revalidatePath("/", "layout");

//     return { success: true };
//   }catch (error) {
//     console.error("Error updating cart:", error);
//     return { success: false, error: "Failed to update cart. Please try again." };
//   }
// }

// export async function decreaseItemFromCart(formData: FormData) {
//   try {
//     const { getUser } = getKindeServerSession();
//     const user = await getUser();

//     // Determine cart ID: Use user ID for logged-in users, or guest_cart_token for guests
//     let cartId: string | undefined;
//     const cookieStore = cookies();

//     if (user) {
//       cartId = user.id;
//     } else {
//       // Retrieve guest_cart_token from cookies
//       cartId = cookieStore.get("guest_cart_token")?.value;

//       if (!cartId) {
//         throw new Error("Guest cart token not found. Unable to process request.");
//       }
//     }

//     const productId = Number(formData.get("productId"));
//     const validatedProductId = productIdSchema.parse(productId);

//     // Retrieve the cart from Redis
//     let cart = await redis.get<Cart>(`cart-${cartId}`);

//     if (!cart) {
//       throw new Error("Cart is empty.");
//     }

//     // Find the item in the cart
//     const existingItem = cart.items.find((item) => item.id === validatedProductId);

//     if (!existingItem) {
//       throw new Error("Item not found in cart.");
//     }

//     if (existingItem.quantity > 1) {
//       // Decrease the quantity if it's greater than 1
//       existingItem.quantity -= 1;
//     } else {
//       // Remove the item from the cart if the quantity is 1
//       cart.items = cart.items.filter((item) => item.id !== validatedProductId);
//     }

//     // Update the cart in Redis
//     await redis.set(`cart-${cartId}`, cart);

//     // Revalidate paths as needed
//     revalidatePath("/", "layout");

//     return { success: true };
//   } catch (error) {
//     console.error("Error decreasing item from cart:", error);
//     return { success: false, error: error || "Failed to update cart. Please try again." };
//   }
// }

// export async function deleteItemFromCart(formData: FormData) {
//   try {
//     const { getUser } = getKindeServerSession();
//     const user = await getUser();

//     // Determine cart ID: Use user ID for logged-in users, or guest_cart_token for guests
//     let cartId: string | undefined;
//     const cookieStore = cookies();

//     if (user) {
//       cartId = user.id;
//     } else {
//       // Retrieve guest_cart_token from cookies
//       cartId = cookieStore.get("guest_cart_token")?.value;

//       if (!cartId) {
//         throw new Error("Guest cart token not found. Unable to process request.");
//       }
//     }

//     // Validate and parse productId
//     const productId = Number(formData.get("productId"));
//     const validatedProductId = productIdSchema.parse(productId);

//     // Retrieve the cart from Redis
//     let cart = await redis.get<Cart>(`cart-${cartId}`);

//     if (!cart || !cart.items.length) {
//       throw new Error("Cart is empty.");
//     }

//     // Filter out the item to delete
//     cart.items = cart.items.filter((item) => item.id !== validatedProductId);

//     // Update the cart in Redis
//     await redis.set(`cart-${cartId}`, cart);

//     // Revalidate paths as needed
//     revalidatePath("/bag");

//     return { success: true };
//   } catch (error) {
//     console.error("Error deleting item from cart:", error);
//     return { success: false, error: error || "Failed to delete item from the cart. Please try again." };
//   }
// }