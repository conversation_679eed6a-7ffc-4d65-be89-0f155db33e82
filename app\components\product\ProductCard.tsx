"use client";

import { motion } from "framer-motion";
import { cn, formatPriceRON, getStockStatus } from "@/lib/utils";
import { ProductCardInterface } from "@/types/product";
import Image from "next/image";
import WishlistButton from "../wishlist/WishlistButton";
import CartButtonProduct from "../cart/CartButtonProduct";

export function ProductCard({ product, index }: { product: ProductCardInterface; index: number }) {

    const stockStatus = getStockStatus(product.stock);

    return(
        <>
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="flex-none snap-start group/card relative  rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 h-full"
              >
                <div
                  key={product.id}
                  className="group/card relative rounded-lg border dark:border-gray-700 overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 flex flex-col h-full"
                >
                  <div className="relative aspect-w-4 aspect-h-3 h-48">
                    <Image
                      src={product.ImageUrl || "https://op47vimj99.ufs.sh/f/6Hnm5nafTbm9yw7hTO5p9dqwM0gSzW5riAs8G7cYPaytnUOu"}
                      alt={product.Description_Local}
                      width={500}
                      height={500}
                      className="w-full h-full object-cover transform group-hover/card:scale-105 transition-transform duration-300"
                    />
                      {/* de test  */}
                      {/* <div className="absolute top-2 left-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded">
                        Oferta
                      </div> */}

                    {product.HasDiscount && product.activeDiscountType && product.activeDiscountValue && (
                      <div className="absolute top-2 left-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded">
                        {product.activeDiscountType === "PERCENTAGE" && `-${product.activeDiscountValue}%`}
                        {product.activeDiscountType === "FIXED_AMOUNT" && `-${product.activeDiscountValue} RON`}
                        {product.activeDiscountType === "NEW_PRICE" && `${product.activeDiscountValue} RON`}
                      </div>
                    )}
                    <div className="absolute top-2 right-2 space-y-2">
                    {/* <button className="p-2 rounded-full bg-white/90 hover:bg-white shadow-sm transition-colors">
                      <Heart className="w-5 h-5 text-gray-600 hover:text-red-500 transition-colors" />
                    </button> */}
                    <WishlistButton material_number={product.Material_Number} initialIsInWishlist={product.isInWishlist ?? false} />
                  </div>
                  </div>

                  <div className="p-4 flex-grow flex flex-col">
                    <div className="text-sm  mb-1">
                      {product.categoryLevel3}
                    </div>
                    <h3 className="text-lg font-semibold  mb-1 line-clamp-2">
                      {product.Description_Local}
                    </h3>
                    <div className="text-xs  mb-2 flex-grow">
                      {product.VehicleModel && (
                        <p className="mb-1 font-medium line-clamp-1">Compatibil cu: {product.VehicleModel.join("/")}</p>
                      )}
                    </div>

                    <div className="flex items-center justify-between mt-auto">
                      <div className="flex items-baseline gap-2">
                        <span className="text-lg font-bold ">
                          {formatPriceRON(product.FinalPrice)}
                        </span>
                        {product.HasDiscount && (
                          <span className="text-sm text-gray-500 line-through">
                            {formatPriceRON(product.PretAM)}
                          </span>
                        )}
                      </div>
                        <CartButtonProduct product={product.Material_Number} />
                    </div>

                    <div className="mt-3 flex items-center justify-between text-sm">
                      <div className="text-xs font-medium px-2 py-1 bg-gray-100 dark:bg-gray-400 dark:text-black rounded-md">
                        OE: {product.Material_Number}
                      </div>
                      {/* <span
                        className={cn(
                          "text-xs",
                          product.stockStatus === "IN_STOCK"
                            ? "text-green-600"
                            : product.stockStatus === "LOW_STOCK"
                              ? "text-yellow-600"
                              : "text-red-600",
                        )}
                      >
                        {product.stockStatus === "IN_STOCK" ? "In Stock" : product.stockStatus === "LOW_STOCK" ? "Low Stock" : "Out of Stock"}
                      </span> */}
                    <span
                      className={cn(
                        "px-2 py-1 rounded-full text-xs",
                        stockStatus === "IN_STOCK"
                          ? "bg-green-100 text-green-800"
                          : stockStatus === "LOW_STOCK"
                            ? "bg-yellow-100 text-yellow-800"
                            : "bg-red-100 text-red-800",
                      )}
                    >
                      {stockStatus.replace(/_/g, " ")}
                    </span>                      
                    </div>
                  </div>
                </div>


                {/* <div className="relative aspect-w-4 aspect-h-3">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-full object-cover transform group-hover/card:scale-105 transition-transform duration-300"
                  />
                  {product.isOnSale && (
                    <Badge className="absolute top-2 left-2 bg-red-500">
                      Sale
                    </Badge>
                  )}
                  <div className="absolute top-2 right-2 space-y-2">
                    <button className="p-2 rounded-full bg-white/90 hover:bg-white shadow-sm transition-colors">
                      <Heart className="w-5 h-5 text-gray-600 hover:text-red-500 transition-colors" />
                    </button>
                  </div>
                </div>

                <div className="p-4">
                  <div className="text-sm text-gray-500 mb-1">
                    {product.category}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">
                    {product.name}
                  </h3>
                  <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                    {product.description}
                  </p>

                  <div className="flex items-center justify-between mt-4">
                    <div className="flex items-baseline gap-2">
                      <span className="text-xl font-bold text-gray-900">
                        ${product.price.toLocaleString()}
                      </span>
                      {product.originalPrice && (
                        <span className="text-sm text-gray-500 line-through">
                          ${product.originalPrice.toLocaleString()}
                        </span>
                      )}
                    </div>
                    <button className="p-2 rounded-full bg-[#0066B1] hover:bg-[#004d85] text-white shadow-sm transition-colors">
                      <ShoppingCart className="w-5 h-5" />
                    </button>
                  </div>

                  <div className="mt-3 flex items-center justify-between text-sm">
                    <div className="flex items-center">
                      <div className="text-xs font-medium px-2 py-1 bg-gray-100 rounded-md text-gray-700">
                        OE:{" "}
                        {product.id === "m-sport-brake-kit"
                          ? "34112450562"
                          : product.id === "kw-v3-coilovers"
                            ? "31302450678"
                            : "13717450509"}
                      </div>
                    </div>
                    <span
                      className={cn(
                        "px-2 py-1 rounded-full text-xs",
                        product.stockStatus === "In Stock"
                          ? "bg-green-100 text-green-800"
                          : product.stockStatus === "Low Stock"
                            ? "bg-yellow-100 text-yellow-800"
                            : "bg-red-100 text-red-800",
                      )}
                    >
                      {product.stockStatus}
                    </span>
                  </div>
                </div> */}
              </motion.div>
        </>
    )
}   



