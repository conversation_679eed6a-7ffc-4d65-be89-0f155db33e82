"server-only"

import { prisma, withRetry } from "@/lib/db"
import { logger } from "@/lib/logger"
import { getCachedData } from "@/lib/redis-cache"
import { cuidSchema, productCodSchema, userIdClerkSchema, userIdStringSchema } from "@/lib/zod"


// Get wishlist items for the current user on the Wishlist route
export async function getWishlistItems(userIdDb: string){
  if (!userIdDb){
    logger.warn(`[getWishlistItems] No paramater provided`)
    return []
  }

  try{
    const userIdParsed = userIdStringSchema.safeParse(userIdDb)

    if (!userIdParsed.success) {
      logger.error("Invalid user ID provided:", userIdParsed.error.format());
      return []; // Return empty array for invalid IDs
    }

    const userId = userIdParsed.data
    
    const wishlistItems = await withRetry(() => prisma.wishlist.findMany({
        where: {
          userId,
        },
        include: {
          product: {
            select: {
              ImageUrl: true,
              Material_Number: true,
              Description_Local: true,
              FinalPrice: true
            }
          }, 
        },
        orderBy: {
          createdAt: "desc", 
        },
      })
    )
    
    // const wishlistItems = await withRetry(() => prisma.wishlist.findMany({
    //     where: { userId },
    //     select: { productCode: true }
    //   })
    // )
    
    return wishlistItems
    //return wishlistItems.map(item => item.productCode)
  }catch(e){
    logger.error(`[getWishlistItems] Error trying to get the wishlist for userId ${userIdDb}: ${e}`)
    return []
  }
}

//Get wishlist count for the Navbar button
export async function getWishlistCount(userIdDb: string): Promise<number>{
  
  if (!userIdDb){
    logger.warn(`[getWishlistCount] No paramater provided`)
    return 0
  }

  try{
    //const userIdParsed = cuidSchema.safeParse(userIdDb)
    const userIdParsed = cuidSchema.safeParse(userIdDb)

    if (!userIdParsed.success) {
      logger.warn("[getWishlistItems] Invalid user ID format")
      return 0; // Return empty array for invalid IDs
    }

    const userId = userIdParsed.data
    const cacheKey = `wishlist-count:${userId}`;

    const wishlistCount = await getCachedData(
      cacheKey,
      async () => {
        // The DB call is now inside the try...catch block
        return withRetry(() =>
          prisma.wishlist.count({
            where: { userId }
          })
        );
      }
    )

    // const wishlistCount = await withRetry(() =>
    //       prisma.wishlist.count({
    //         where: { userId }
    //       })
    //     );

    if (!wishlistCount) return 0
    
    return wishlistCount
  }catch(e){
    logger.error(`[getWishlistCount] Error trying to get the wishlist count for userId ${userIdDb}: ${e}`)
    return 0
  }
}

export async function isProductInWishlist(userIdDb: string, productCodeRaw: string): Promise<boolean> {
  if (!userIdDb || !productCodeRaw) {
    logger.warn("[isProductInWishlist] Missing userId or productCode");
    return false;
  }

  const userIdParsed = cuidSchema.safeParse(userIdDb);
  const productCodeParsed = productCodSchema.safeParse(productCodeRaw);

  if (!userIdParsed.success || !productCodeParsed.success) {
    logger.error("[isProductInWishlist] Invalid inputs", {
      productCodeError: productCodeParsed.error?.format?.(),
    });
    return false;
  }

  const userId = userIdParsed.data;
  const productCode = productCodeParsed.data;

  try {
    const existingItem = await withRetry(() =>
      prisma.wishlist.findUnique({
        where: {
          userId_productCode: {
            userId,
            productCode,
          },
        },
        select: { id: true }, // only fetch what you need
      })
    );

    return !!existingItem;
  } catch (e) {
    logger.error(`[isProductInWishlist] Error for userId ${userId} and productCode ${productCode}:`, e);
    return false;
  }
}

//used in landing page
export async function getWishlistProductCodes(userId: string): Promise<Set<string>> {
  const userIdParsed = cuidSchema.safeParse(userId);

  if (!userIdParsed.success) {
    logger.error("[getWishlistProductCodes] Invalid userId", userIdParsed.error.format());
    return new Set();
  }

  try {
    const wishlist = await withRetry(() =>
      prisma.wishlist.findMany({
        where: { userId: userIdParsed.data },
        select: { productCode: true },
      })
    );

    return new Set(wishlist.map((w) => w.productCode));
  } catch (e) {
    logger.error(`[getWishlistProductCodes] DB error for user ${userId}:`, e);
    return new Set();
  }
}