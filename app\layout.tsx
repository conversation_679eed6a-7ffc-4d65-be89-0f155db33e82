import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { ClerkProvider } from "@clerk/nextjs";
import { MainNavbar } from "./components/navbar/MainNavbar";
import MegaMenuAndCategory from "./components/navbar/MegaMenuCategory";
import { Providers } from "./providers";
import { Toaster } from "@/components/ui/sonner";
import { WishlistProvider } from "./context/WishlistContext";
import { getWishlistProductCodes } from "./getData/wishlist";
import { getCartItems } from "./getData/cart";
import { getCurrentDbUser } from "@/lib/auth";

import { CartProvider } from "./context/CartContext";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Automobile Bavaria : Piese auto BMW originale",
  description: "Piese auto BMW originale",
};


export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {

  
  const userId = await getCurrentDbUser()

  let wishlistProductCodes = new Set<string>();
  let cartItems: any[] = [];

  if (userId) {
      wishlistProductCodes = await getWishlistProductCodes(userId.id);
      cartItems = await getCartItems(userId.id);
  }

  // Convert Set for the context (already a Set)
  const initialWishlistItems = wishlistProductCodes;

  return (
    <ClerkProvider>
      <html lang="en" suppressHydrationWarning>
        <body
          className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        >
          <Providers>
            <CartProvider initialItems={cartItems}>
            <WishlistProvider initialItems={initialWishlistItems}>
            <div className="relative w-full shadow-md">
              <div className="max-w-[1640px] mx-auto">
                <MainNavbar searchParams={{ query: "" }} />
                <MegaMenuAndCategory />
              </div>
            </div>
              <div className="relative w-full">
                {children}
              </div>
              <Toaster />
            </WishlistProvider>
            </CartProvider>
          </Providers>
        </body>
      </html>
    </ClerkProvider>
  );
}


