import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { ClerkProvider } from "@clerk/nextjs";
import { MainNavbar } from "./components/navbar/MainNavbar";
import MegaMenuAndCategory from "./components/navbar/MegaMenuCategory";
import { Providers } from "./providers";
import { Toaster } from "@/components/ui/sonner";
import { WishlistProvider } from "./context/WishlistContext";
import { getWishlistCount, getWishlistProductCodes } from "./getData/wishlist";
import { getCurrentDbUser } from "@/lib/auth";
import { getCartCount } from "./getData/cart";
import { CartProvider } from "./context/CartContext";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Automobile Bavaria : Piese auto BMW originale",
  description: "Piese auto BMW originale",
};


export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {

  
  const userId = await getCurrentDbUser()

  let wishlistCount  = 0;
  let bagCount = 0;
  let wishlistProductCodes = new Set<string>();

  if (userId) {
      wishlistCount  = await getWishlistCount(userId.id);
      bagCount = await getCartCount(userId.id);
      wishlistProductCodes = await getWishlistProductCodes(userId.id);
  }

  // Convert Set to Map for the context
  const initialWishlistItems = new Map<string, boolean>();
  wishlistProductCodes.forEach(productCode => {
    initialWishlistItems.set(productCode, true);
  });

  return (
    <ClerkProvider>
      <html lang="en" suppressHydrationWarning>
        <body
          className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        >
          <Providers>
            <CartProvider initialCount={bagCount}>
            <WishlistProvider initialCount={wishlistCount} initialItems={initialWishlistItems}>
            <div className="relative w-full shadow-md">
              <div className="max-w-[1640px] mx-auto">
                <MainNavbar searchParams={{ query: "" }} />
                <MegaMenuAndCategory />
              </div>
            </div>
              <div className="relative w-full">
                {children}
              </div>
              <Toaster />
            </WishlistProvider>
            </CartProvider>
          </Providers>
        </body>
      </html>
    </ClerkProvider>
  );
}


