"use server"

import { StockStatus } from '@/generated/prisma';
import { Decimal } from '@/generated/prisma/runtime/library';
import { prisma, withRetry } from '@/lib/db';

export interface SearchSuggestions {
    ImageUrl: string[],
    id: string,
    FinalPrice: Decimal | null,
    Description_Local: string | null,
    Material_Number: string,
    HasDiscount: boolean,
    activeDiscountValue: Decimal | null,
    discountPercentage: Decimal | null,
    stockStatus: StockStatus,
}

export async function searchSuggestionsAction(formData: FormData): Promise<SearchSuggestions[]>{

  try {
    const query = formData.get("query") as string;

    // Fetch from the database
    const suggestions = await withRetry(() => prisma.product.findMany({
      where: {
        OR: [
          { Description_Local_normalized: { contains: query, mode: "insensitive" } },
          { Material_Number: { contains: query, mode: "insensitive" } },
        ],
      },
      select: {
        ImageUrl: true,
        id: true,
        FinalPrice: true,
        Description_Local: true,
        Material_Number: true,
        HasDiscount: true,
        activeDiscountValue: true,
        discountPercentage: true,
        stockStatus: true,
      },
      take: 5, // Limit the number of suggestions
    }))

    if(suggestions.length === 0) return []
  
    return suggestions;
  }catch (error) {
    console.error("Error in searchSuggestionsAction:", error);
    return [];
  }
}