"use client"

import { Trash } from "lucide-react"
import { useRouter } from 'next/navigation';
import { useTransition } from 'react';
import { toast } from "sonner"
import { useWishlist } from '@/app/context/WishlistContext';
import { Button } from '@/components/ui/button';
import { removeFromWishlist } from "@/app/actions/wishlist";

export default function RemoveProductWishlist({product}: {product: string}){

  const { wishlistCount, setWishlistCount } = useWishlist();
  const [isPending, startTransition] = useTransition();

  let debounceTimer: NodeJS.Timeout;  
  const router = useRouter()

  const handleWishlistToggle = () => {
    clearTimeout(debounceTimer);
    debounceTimer = setTimeout(() => {
        startTransition(async () => {
        try {
            const response = await removeFromWishlist(product);
            setWishlistCount(wishlistCount - 1)
            if(response.success === true) {
                toast.success('Sters cu succes din lista de favorite.');
            }        
             
            router.refresh()
        } catch (error) {
            toast.error('Nu s-a putut actualiza lista de favorite.');
        }
        });
    }, 300)
  };
    
    return(
        <>
        <Button
            onClick={handleWishlistToggle}
            //disabled={isPending}
            aria-label={"Sterge din favorite"}
            variant="outline"
            size="sm"
            className="gap-2 text-red-600 hover:text-red-700"
            >
            <Trash className="w-4 h-4" /> Remove
        </Button>
        </>
    )
}