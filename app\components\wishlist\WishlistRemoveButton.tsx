"use client"

import { Trash } from "lucide-react"
import { useRouter } from 'next/navigation';
import { useTransition, useOptimistic } from 'react';
import { toast } from "sonner"
import { useWishlist } from '@/app/context/WishlistContext';
import { Button } from '@/components/ui/button';
import { removeFromWishlist } from "@/app/actions/wishlist";

export default function RemoveProductWishlist({product}: {product: string}){

  const {
    removeFromWishlistOptimistic,
    revertWishlistChange,
    wishlistItems
  } = useWishlist();
  const [isPending, startTransition] = useTransition();
  const router = useRouter();

  // Track optimistic removal state
  const [optimisticRemoved, setOptimisticRemoved] = useOptimistic(
    false,
    (_state, newState: boolean) => newState
  );

  const handleWishlistToggle = () => {
    const previousState = wishlistItems.get(product) ?? true; // Assume it's in wishlist if we're showing remove button

    // Optimistic update
    setOptimisticRemoved(true);
    removeFromWishlistOptimistic(product);

    startTransition(async () => {
      try {
        const response = await removeFromWishlist(product);
        if (response.success) {
          toast.success('Șters cu succes din lista de favorite.');
          router.refresh();
        } else {
          throw new Error('Failed to remove from wishlist');
        }
      } catch (error) {
        // Revert optimistic updates on error
        setOptimisticRemoved(false);
        revertWishlistChange(product, previousState);
        toast.error('Nu s-a putut actualiza lista de favorite.');
        console.error('Remove from wishlist failed:', error);
      }
    });
  };
    
    // Don't render if optimistically removed
    if (optimisticRemoved) {
      return null;
    }

    return(
        <>
        <Button
            onClick={handleWishlistToggle}
            disabled={isPending}
            aria-label={"Sterge din favorite"}
            variant="outline"
            size="sm"
            className="gap-2 text-red-600 hover:text-red-700 disabled:opacity-50"
            >
            <Trash className="w-4 h-4" />
            {isPending ? 'Se șterge...' : 'Remove'}
        </Button>
        </>
    )
}