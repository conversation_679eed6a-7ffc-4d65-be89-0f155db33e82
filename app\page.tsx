
import { getCurrentDbUser } from "@/lib/auth";
import ProductGrid from "./components/product/ProductGrid";
import { getRecentOrders } from "./getData/user";
import { getFeaturedProducts } from "./getData/products";
import CategoryGrid from "./components/categories/CategoryGridLandingPage";
import HeroSection from "./components/banner/HeroSection";
import { getCategorySectionLandingPageBanners, getHeroBanners } from "./getData/banners";
import { getWishlistProductCodes } from "./getData/wishlist";
import { redirect } from "next/navigation";
import { ProductCardInterface } from "@/types/product";
import { getStockForProduct } from "./getData/stock";

export default async function Home() {

   const user = await getCurrentDbUser();

   if(!user){
    return redirect("sign-in")
   }

   const ordered = user ? await getRecentOrders(user.id) : [];

   const featured: ProductCardInterface[] = await getFeaturedProducts()
   

  // Step 1: Get wishlist product codes
  const wishlistProductCodes = user
    ? await getWishlistProductCodes(user.id)
    : new Set<string>();

  // Step 2: Get stock info in parallel
  const stockStatuses = await Promise.all(
    featured.map((p) => getStockForProduct(p.Material_Number))
  );

  const productsWithData = featured.map((product, index) => ({
    ...product,
    isInWishlist: wishlistProductCodes.has(product.Material_Number),
    stock: stockStatuses[index], // stock is 0 or 1
  }));

   const heroBanners = await getHeroBanners()

   const categories = await getCategorySectionLandingPageBanners()

  return (
    <>
      {/* <HeroSection heroBanners={heroBanners} />

      <CategoryGrid  categories={categories} /> */}

      {ordered.length > 0 && <ProductGrid products={ordered} title="Produse recente" description="Produse pe care le-ai cumparat recent" />  }

      {featured.length > 0 && <ProductGrid products={productsWithData} title="Produse recomandate" description="Produse care te-ar putea interesa" /> } 

    </>
  );
}


