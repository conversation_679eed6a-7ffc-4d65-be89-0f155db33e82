"use client";

import { updateCartActiondata } from "@/app/actions/cart";
import { useCart } from "@/app/context/CartContext";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { CartItem } from "@/types/cart";
import { Minus, Plus, Trash } from "lucide-react";
import Image from "next/image";
import { useState } from "react";

export default function CartPageItem({ item }: { item: CartItem }) {
    const [localItem, setLocalItem] = useState(item);
    const [isUpdating, setIsUpdating] = useState(false);
    const { setCartCount } = useCart();


    const handleUpdate = async (updates: Partial<typeof item>) => {
        setIsUpdating(true);
      const updatedItem = { ...localItem, ...updates };
      setLocalItem(updatedItem); // Optimistically update the UI
      await updateCartActiondata({ itemId: item.id, ...updates });
      setIsUpdating(false); // Reset the state after the update is complete
    };
  return ( 
    <>               
        <Checkbox
            checked={localItem.addToOrder}
            onCheckedChange={(checked) => handleUpdate({ addToOrder: !!checked })}
            disabled={isUpdating}
        /> 
        <div className="w-24 h-24 rounded-lg overflow-hidden">
            <Image
                width={100}
                height={100}
                src={item.ImageUrl}
                alt={item.Description_Local}
                className="w-full h-full object-cover"
            />
        </div>

        <div className="flex-1">
            <h3 className="text-lg font-medium">{item.Description_Local}</h3>
            <p className="text-sm text-gray-500 mt-1">
                OE: {item.Material_Number}
            </p>
            <div className="flex items-center mt-2 gap-6">
                <Input
                    value={localItem.vinNotes || ''}
                    onChange={(e) => handleUpdate({ vinNotes: e.target.value })}
                    id={`obs-${item.id}`}
                    placeholder="Observatii sau VIN"
                    className=" border-gray-300 focus:ring-1 focus:ring-gray-400"
                    disabled={isUpdating}
                />
                <div className="flex items-center space-x-2">
                    <Switch
                        disabled={isUpdating}
                        id="adauga-pe-factura" 
                        checked={localItem.addVinNotesToInvoice}
                        onCheckedChange={(checked) => handleUpdate({ addVinNotesToInvoice: checked })}    
                    />
                    <Label htmlFor="adauga-pe-factura" className="text-xs text-gray-500">Adauga pe factura</Label>
                </div>
            </div>
            <div className="flex items-center gap-4 mt-4">
                <div className="flex items-center gap-2">
                    <Button disabled={isUpdating} variant="outline" size="icon" className="h-8 w-8" onClick={() => { handleUpdate({ quantity: Math.max(1, item.quantity - 1) })}}>
                        <Minus className="w-4 h-4" />
                    </Button>
                    <Input
                        value={localItem.quantity} 
                        onChange={(e) => handleUpdate({ quantity: Number(e.target.value) })}
                        className="w-16 h-8 text-center"
                        min="1"
                        disabled={isUpdating} // Disable input while updating
                      />
                    <Button disabled={isUpdating} variant="outline" size="icon" className="h-8 w-8" onClick={() => handleUpdate({ quantity: item.quantity + 1 })}>
                        <Plus className="w-4 h-4" />
                    </Button>
                </div>
                <Button
                    variant="outline"
                    size="sm"
                    className="gap-2 text-red-600 hover:text-red-700"
                    onClick={() => handleUpdate({ quantity: 0 })}
                    disabled={isUpdating}
                >
                    <Trash className="w-4 h-4" /> Sterge
                </Button>
            </div>
        </div>
    </>
  )}