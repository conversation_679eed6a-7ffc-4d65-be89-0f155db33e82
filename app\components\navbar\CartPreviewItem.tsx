import { updateCartActiondata } from "@/app/actions/cart";
import { useCart } from "@/app/context/CartContext";
import { Button } from "@/components/ui/button";
import { formatPriceRON } from "@/lib/utils";
import { CartItem } from "@/types/cart";
import { X } from "lucide-react";
import Image from "next/image";
import { useTransition } from "react";
import { toast } from "sonner";

export default function CartPreviewItem({ item }: { item: CartItem }) {
    const [isPending, startTransition] = useTransition();
    const {
        removeFromCartOptimistic,
        revertOptimisticUpdate,
        cartItems,
        getCartItem
    } = useCart();

    // Get the current item state from context (includes optimistic updates)
    const currentItem = getCartItem(item.id);

    // If item was optimistically removed, don't render it
    if (!currentItem) {
        return null;
    }

    const handleRemove = () => {
        startTransition(async () => {
            const previousCartItems = [...cartItems];

            // Optimistically remove the item
            removeFromCartOptimistic(item.id);

            try {
                const response = await updateCartActiondata({ itemId: item.id, quantity: 0 });
                if (response.success) {
                    toast.success('Produs eliminat din coș.');
                } else {
                    throw new Error('Failed to remove item');
                }
            } catch (error) {
                // Revert optimistic update on error
                revertOptimisticUpdate(previousCartItems);
                toast.error('Nu s-a putut elimina produsul. Încearcă din nou.');
                console.error('Cart remove failed:', error);
            }
        });
    };

  return (
            <div key={item.id} className="flex gap-3 pb-3 border-b border-gray-100 dark:border-gray-700">
              <div className="w-16 h-16 rounded-md overflow-hidden flex-shrink-0">
                <Image
                  width={100}
                  height={100}
                  src={item.ImageUrl}
                  alt={item.Description_Local}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">{item.Description_Local}</h4>
                <p className="text-xs text-gray-500 dark:text-gray-400">{currentItem.quantity} x {formatPriceRON(currentItem.FinalPrice)}</p>
                <p className="text-sm font-medium">${formatPriceRON(currentItem.FinalPrice * currentItem.quantity)}</p>
              </div>
              <div className="flex flex-col gap-2">
                <Button disabled={isPending} variant="ghost" size="icon" className="gap-2" onClick={handleRemove}>
                 <div className="w-6 h-6 flex items-center justify-center rounded-full bg-white/30 backdrop-blur-sm hover:bg-red-500 hover:text-white transition">
  <X className="w-4 h-4" />
</div>
                </Button>
              </div>
            </div>
            )
}
