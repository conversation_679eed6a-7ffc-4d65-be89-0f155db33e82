
'use client';

import { addToWishlist, removeFromWishlist } from '@/app/actions/wishlist';
import { motion } from "framer-motion";
import { cn } from '@/lib/utils';
import { Heart } from 'lucide-react';
import { useTransition } from 'react';
import { toast } from "sonner";
import { useWishlist } from '@/app/context/WishlistContext';
import { Button } from '@/components/ui/button';

export default function WishlistButton({
  material_number
}: {
  material_number: string
}) {
  const [isPending, startTransition] = useTransition();
  const {
    isInWishlist,
    addToWishlistOptimistic,
    removeFromWishlistOptimistic,
    revertOptimisticUpdate,
    updateActualWishlist
  } = useWishlist();

  // Check if item is in wishlist (context handles the optimistic state)
  const optimisticIsInWishlist = isInWishlist(material_number);

  const handleWishlistToggle = () => {
    startTransition(async () => {
      const wasInWishlist = optimisticIsInWishlist;

      // Optimistically update the UI
      if (wasInWishlist) {
        removeFromWishlistOptimistic(material_number);
      } else {
        addToWishlistOptimistic(material_number);
      }

      try {
        let result;
        if (wasInWishlist) {
          result = await removeFromWishlist(material_number);
        } else {
          result = await addToWishlist(material_number);
        }

        // If the server action succeeded, update the actual wishlist
        if (result.success) {
          updateActualWishlist(material_number, !wasInWishlist);
          toast.success(
            wasInWishlist
              ? 'Eliminat din lista de favorite!'
              : 'Adăugat în lista de favorite!'
          );
        } else {
          // If the server action failed, revert the optimistic update
          revertOptimisticUpdate(material_number, wasInWishlist);
          toast.error(
            wasInWishlist
              ? 'Nu s-a putut elimina din lista de favorite. Încearcă din nou.'
              : 'Nu s-a putut adăuga în lista de favorite. Încearcă din nou.'
          );
        }
      } catch (error) {
        // If there was an error, revert the optimistic update
        console.error('Wishlist action failed:', error);
        revertOptimisticUpdate(material_number, wasInWishlist);
        toast.error(
          wasInWishlist
            ? 'Nu s-a putut elimina din lista de favorite. Încearcă din nou.'
            : 'Nu s-a putut adăuga în lista de favorite. Încearcă din nou.'
        );
      }
    });
  };

  return (<>
    {/* <button
      type="button"
      onClick={handleWishlistToggle}
      //disabled={isPending}
      aria-label={optimisticIsInWishlist ? "Remove from wishlist" : "Add to wishlist"}
      className={cn(
        "p-2 rounded-full bg-white/90 hover:bg-white shadow-sm transition-all duration-200",
        isPending && "opacity-70 cursor-not-allowed"
      )}
    >
      <motion.div
        key={optimisticIsInWishlist ? "in" : "out"}
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.8, opacity: 0 }}
        transition={{ type: "spring", stiffness: 400, damping: 25 }}
      >
        <Heart
          className={cn(
            "w-5 h-5 transition-all duration-200",
            optimisticIsInWishlist
              ? "text-red-500 fill-red-500"
              : "text-gray-600 fill-gray-300 hover:text-red-500 hover:fill-red-500",
            //isPending && "animate-pulse"
          )}
        />
      </motion.div>
    </button> */}
    <Button
      type='button'
      onClick={handleWishlistToggle}
      //disabled={isPending} // <-- Disable the button during the transition
      aria-label={optimisticIsInWishlist ? "Sterge din favorite" : "Adauga la favorite"}
      className="p-2 rounded-full bg-white/90 hover:bg-white shadow-sm transition-colors"
    >
      <motion.div
        key={optimisticIsInWishlist ? "in" : "out"}
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }} // scale: 1 is usually better than 1.2
        exit={{ scale: 0.8, opacity: 0 }}
        transition={{ type: "spring", stiffness: 400, damping: 25 }}
      >
        <Heart
          className={cn(
            "w-5 h-5 transition-colors",
            optimisticIsInWishlist  ? "text-red-500" : "text-gray-600 hover:text-red-500",
            isPending && "animate-pulse" //<-- Add pulse animation during transition (optional)
          )}
        />
      </motion.div>
    </Button>
    </>
  );
}



