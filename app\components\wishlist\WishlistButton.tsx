
'use client';

import { addToWishlist, removeFromWishlist } from '@/app/actions/wishlist';
import { motion } from "framer-motion";
import { cn } from '@/lib/utils';
import { Heart } from 'lucide-react';
import { useOptimistic, useTransition, useEffect } from 'react';
import { toast } from "sonner";
import { useWishlist } from '@/app/context/WishlistContext';

export default function WishlistButton({
  material_number,
  initialIsInWishlist
}: {
  material_number: string,
  initialIsInWishlist: boolean
}) {
  const {
    wishlistItems,
    addToWishlistOptimistic,
    removeFromWishlistOptimistic,
    revertWishlistChange,
    updateWishlistItem
  } = useWishlist();

  const [isPending, startTransition] = useTransition();

  // Get current wishlist state from context, fallback to initial prop
  const currentIsInWishlist = wishlistItems.get(material_number) ?? initialIsInWishlist;

  // Use optimistic state for immediate UI updates
  const [optimisticIsInWishlist, setOptimisticIsInWishlist] = useOptimistic(
    currentIsInWishlist,
    (_state, newState: boolean) => newState
  );

  // Initialize the wishlist item in context if not already present
  useEffect(() => {
    if (!wishlistItems.has(material_number)) {
      updateWishlistItem(material_number, initialIsInWishlist);
    }
  }, [material_number, initialIsInWishlist, wishlistItems, updateWishlistItem]);

  const handleWishlistToggle = () => {
    const previousState = currentIsInWishlist;
    const newState = !previousState;

    startTransition(async () => {
      // Optimistic updates inside transition
      setOptimisticIsInWishlist(newState);

      // Update context optimistically
      if (newState) {
        addToWishlistOptimistic(material_number);
      } else {
        removeFromWishlistOptimistic(material_number);
      }

      try {
        let response;
        if (newState) {
          response = await addToWishlist(material_number);
          if (response.success) {
            toast.success('Adăugat în lista de favorite!');
          } else {
            throw new Error('Failed to add to wishlist');
          }
        } else {
          response = await removeFromWishlist(material_number);
          if (response.success) {
            toast.success('Eliminat din lista de favorite!');
          } else {
            throw new Error('Failed to remove from wishlist');
          }
        }
      } catch (error) {
        // Revert optimistic updates on error
        setOptimisticIsInWishlist(previousState);
        revertWishlistChange(material_number, previousState);

        toast.error(
          newState
            ? 'Nu s-a putut adăuga în lista de favorite. Încearcă din nou.'
            : 'Nu s-a putut elimina din lista de favorite. Încearcă din nou.'
        );
        console.error('Wishlist action failed:', error);
      }
    });
  };

  return (
    <button
      type="button"
      onClick={handleWishlistToggle}
      disabled={isPending}
      aria-label={optimisticIsInWishlist ? "Remove from wishlist" : "Add to wishlist"}
      className={cn(
        "p-2 rounded-full bg-white/90 hover:bg-white shadow-sm transition-all duration-200",
        isPending && "opacity-70 cursor-not-allowed"
      )}
    >
      <motion.div
        key={optimisticIsInWishlist ? "in" : "out"}
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.8, opacity: 0 }}
        transition={{ type: "spring", stiffness: 400, damping: 25 }}
      >
        <Heart
          className={cn(
            "w-5 h-5 transition-all duration-200",
            optimisticIsInWishlist
              ? "text-red-500 fill-red-500"
              : "text-gray-600 fill-gray-300 hover:text-red-500 hover:fill-red-500",
            isPending && "animate-pulse"
          )}
        />
      </motion.div>
    </button>
  );
}



