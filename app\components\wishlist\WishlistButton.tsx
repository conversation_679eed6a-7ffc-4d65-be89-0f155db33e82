
'use client';

import { addToWishlist, removeFromWishlist } from '@/app/actions/wishlist';
import { motion } from "framer-motion";
import { cn } from '@/lib/utils';
import { Heart } from 'lucide-react';
import { useOptimistic, useTransition, useRef } from 'react'; // Import useRef
import { toast } from "sonner";
import { useWishlist } from '@/app/context/WishlistContext';
import { Button } from '@/components/ui/button';

export default function WishlistButton({ material_number, initialIsInWishlist }: { material_number: string, initialIsInWishlist: boolean }) {

const handleWishlistToggle = () => {
  };

  return (
    <button
      type="button"
      onClick={handleWishlistToggle}
      //disabled={isPending} // It's good practice to disable the button to prevent multiple rapid clicks
      aria-label={initialIsInWishlist ? "Remove from wishlist" : "Add to wishlist"}
      className="p-2 rounded-full bg-white/90 hover:bg-white shadow-sm transition-colors"
    >
      {/* The key on motion.div ensures it re-animates on state change */}
      <motion.div
        key={initialIsInWishlist ? "in" : "out"}
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.8, opacity: 0 }}
        transition={{ type: "spring", stiffness: 400, damping: 25 }}
      >
        <Heart
          className={cn(
            "w-5 h-5 transition-all duration-200",
            initialIsInWishlist ? "text-red-500 fill-red-500" : "text-gray-600 fill-gray-300 hover:text-red-500 hover:fill-red-500"
          )}
        />
      </motion.div>
    </button>
  );
}



