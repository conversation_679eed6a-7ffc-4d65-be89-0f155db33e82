"server-only"

import { prisma, withRetry } from "@/lib/db"
import { logger } from "@/lib/logger"
import { getCachedData } from "@/lib/redis-cache"
import { productCodSchema } from "@/lib/zod"
import { LandingPageProduct, ProductPage } from "@/types/product"


export async function getFeaturedProducts(limit = 6): Promise<LandingPageProduct[]> {
  const cacheKey = `featured-products:${limit}`;

  try {
    const productsFromDbOrCache = await getCachedData(
      cacheKey,
      async () => {
        return withRetry(() =>
          prisma.product.findMany({
            where: { IsOnLandingPage: true,
                     isActive: true
             },
            take: limit,
            select: {
              id: true,
              Material_Number: true,
              Description_Local: true,
              PretAM: true,
              FinalPrice: true,
              ImageUrl: true,    
              categoryLevel3: { select: { name: true } },                       
              HasDiscount: true,
              discountPercentage: true,
              activeDiscountType: true,
              activeDiscountValue: true,
              //stockStatus: true,
              productClass: {
                select: {
                  vehicleModels: {
                    select: { vehicleModel: { select: { name: true } } },
                  },
                },
              },
            },
          })
        );
      },
      60 * 30 // 30 minutes TTL (I assume you meant 30, not 1)
    );

    // If the data from cache/db is null or not an array, return an empty array.
    if (!productsFromDbOrCache || !Array.isArray(productsFromDbOrCache)) {
        logger.warn('[getFeaturedProducts] No products found or data is malformed.');
        return [];
    }
  //console.log('[DEBUG] Raw products from DB:', productsFromDbOrCache);
    // The mapping logic remains largely the same, but it's now inside the try block.
    // Any unexpected error during mapping will be caught.
    return productsFromDbOrCache.map((p) => ({
      id: p.id,
      Material_Number: p.Material_Number,
      Description_Local: p.Description_Local,
      PretAM:   p.PretAM?.toNumber()   ?? null,
      FinalPrice: p.FinalPrice?.toNumber() ?? null,
      ImageUrl: p.ImageUrl,

      categoryLevel3: p.categoryLevel3
        ? { name: p.categoryLevel3.name }
        : null,

      HasDiscount: p.HasDiscount,
      discountPercentage: p.discountPercentage?.toNumber() ?? null,
      activeDiscountType: p.activeDiscountType,
      activeDiscountValue:   p.activeDiscountValue?.toNumber() ?? null,

      productClass: p.productClass
        ? {
            vehicleModels: p.productClass.vehicleModels.map(vm => ({
              vehicleModel: { name: vm.vehicleModel.name }
            }))
          }
        : null,
    }));
  } catch (error) {
    logger.error('Failed to get featured products', error as Error, { context: 'getFeaturedProducts', limit });
    return [];
  }
}

export async function getProductByMaterialNumber(material_number: string): Promise<ProductPage | null>{

  if (!material_number) {
    logger.warn('[getProductById] Called with an empty or null productId.');
    return null;
  }

  //validate with zod
  const parsed = productCodSchema.safeParse(material_number);
  if(!parsed.success) {
    logger.error("[getProductById] Invalid productCode:", parsed.error.format());
    return null;
  }

  const Material_Number = parsed.data

  try {
    const rawProduct = await withRetry(() =>
      prisma.product.findUnique({
        where: {
          isActive: true,
          Material_Number , // your param
        },
        select: {
          Material_Number: true,
          Net_Weight: true,
          Description_Local: true,
          Base_Unit_Of_Measur: true,
          Cross_Plant: true,
          New_Material: true,
          PretAM: true,
          FinalPrice: true,
          HasDiscount: true,
          activeDiscountType: true,
          activeDiscountValue: true,
          discountPercentage: true,
          ImageUrl: true,
          attributes: {
            select: {
              key: true,
              value: true,
            },
          },
          productClass: {
            select: {
              vehicleModels: {
                select: {
                  vehicleModel: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
            },
          },
          categoryLevel3: {
            select: {
              name: true,
            },
          },
        },
      })
    );

    if(!rawProduct){
      logger.warn(`[getProductById] Not found product for ${material_number}`)
      return null
    }

    // ✅ Manually map and convert decimals
    const safeProduct: ProductPage = {
      Material_Number: rawProduct.Material_Number,
      Net_Weight: rawProduct.Net_Weight ?? '',
      Description_Local: rawProduct.Description_Local ,
      Base_Unit_Of_Measur: rawProduct.Base_Unit_Of_Measur ?? '',
      Cross_Plant: rawProduct.Cross_Plant,
      New_Material: rawProduct.New_Material,
      PretAM: rawProduct.PretAM ? rawProduct.PretAM.toNumber() : null,
      FinalPrice: rawProduct.FinalPrice ? rawProduct.FinalPrice.toNumber() : null,
      HasDiscount: rawProduct.HasDiscount,
      activeDiscountType: rawProduct.activeDiscountType,
      activeDiscountValue: rawProduct.activeDiscountValue ? rawProduct.activeDiscountValue.toNumber() : null,
      discountPercentage: rawProduct.discountPercentage ? rawProduct.discountPercentage.toNumber() : null,
      ImageUrl: rawProduct.ImageUrl,
      attributes: rawProduct.attributes,
      productClass: rawProduct.productClass,
      categoryLevel3: rawProduct.categoryLevel3,
    };

    
    logger.info(`[getProductById] Found product for ${material_number}`)
    return safeProduct;
  } catch (error) {
    logger.error(`[getProductById] Error fetching product: ${error}`);
    return null;
  }
}

// Search products (no caching for search - results vary too much)
export async function searchProducts(query: string, limit = 20) {
  if (!query || query.length < 2) return []
  
  return withRetry( () =>  prisma.product.findMany({
    where: {
      OR: [
        { Material_Number_normalized: { contains: query.toLowerCase() } },
        { Description_Local_normalized: { contains: query.toLowerCase() } }
      ],
      isActive: true
    },
    take: limit,
    select: {
      Material_Number: true,
      Description_Local: true,
      ImageUrl: true,
      FinalPrice: true
    }
  }))
}

// Get products by category with Redis caching
export async function getProductsByCategory(categoryId: string, page = 1, limit = 20) {
  const skip = (page - 1) * limit
  const cacheKey = `category:${categoryId}:products:${page}:${limit}`
  
  return getCachedData(
    cacheKey,
    async () => {
      const [products, total] = await Promise.all([
        withRetry( () => prisma.product.findMany({
          where: {
            categoryLevel3Id: categoryId,
            isActive: true
          },
          skip,
          take: limit,
          select: {
            Material_Number: true,
            Description_Local: true,
            ImageUrl: true,
            FinalPrice: true,
            PretAM: true,
            HasDiscount: true,
            discountPercentage: true
          },
          orderBy: {
            FinalPrice: 'asc'
          }
        })),
        withRetry( () => prisma.product.count({
          where: {
            categoryLevel3Id: categoryId,
            isActive: true
          }
        }))
      ])
      
      return {
        products,
        pagination: {
          total,
          pages: Math.ceil(total / limit),
          currentPage: page
        }
      }
    },
    60 * 5 // 5 minutes TTL
  )
}
