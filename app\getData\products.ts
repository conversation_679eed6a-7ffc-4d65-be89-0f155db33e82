"server-only"

import { Product, ProductAttribute, ProductClass } from "@/generated/prisma"
import { prisma, withRetry } from "@/lib/db"
import { logger } from "@/lib/logger"
import { getCachedData } from "@/lib/redis-cache"
import { ProductCardInterface } from "@/types/product"

export type ProductWithDetails = Product & {
  attributes: ProductAttribute[];
  productClass: ProductClass | null;
};

export async function getFeaturedProducts(limit = 6): Promise<ProductCardInterface[]> {
  const cacheKey = `featured-products:${limit}`;

  try {
    const productsFromDbOrCache = await getCachedData(
      cacheKey,
      async () => {
        return withRetry(() =>
          prisma.product.findMany({
            where: { IsOnLandingPage: true,
                     isActive: true
             },
            take: limit,
            select: {
              id: true,
              Material_Number: true,
              Description_Local: true,
              PretAM: true,
              FinalPrice: true,
              ImageUrl: true,    
              categoryLevel3: { select: { name: true } },                       
              HasDiscount: true,
              discountPercentage: true,
              activeDiscountType: true,
              activeDiscountValue: true,
              stockStatus: true,
              productClass: {
                select: {
                  vehicleModels: {
                    select: { vehicleModel: { select: { name: true } } },
                  },
                },
              },
            },
          })
        );
      },
      60 * 30 // 30 minutes TTL (I assume you meant 30, not 1)
    );

    // If the data from cache/db is null or not an array, return an empty array.
    if (!productsFromDbOrCache || !Array.isArray(productsFromDbOrCache)) {
        logger.warn('[getFeaturedProducts] No products found or data is malformed.');
        return [];
    }
//console.log('[DEBUG] Raw products from DB:', productsFromDbOrCache);
    // The mapping logic remains largely the same, but it's now inside the try block.
    // Any unexpected error during mapping will be caught.
    return productsFromDbOrCache.map((product) => ({
      id: product.id,
      Material_Number: product.Material_Number,
      Description_Local: product.Description_Local ?? '',
      ImageUrl: product.ImageUrl?.[0] ?? '',
      FinalPrice: product.FinalPrice ? Number(product.FinalPrice) : undefined,
      PretAM: Number(product.PretAM ?? 0),
      HasDiscount: product.HasDiscount ?? false,
      activeDiscountType: product.activeDiscountType ?? undefined,
      activeDiscountValue: product.activeDiscountValue !== null && product.activeDiscountValue !== undefined
        ? String(product.activeDiscountValue)
        : undefined,
      discountPercentage: product.discountPercentage !== null && product.discountPercentage !== undefined
        ? String(product.discountPercentage)
        : undefined,
      stockStatus: product.stockStatus,
      categoryLevel3: product.categoryLevel3?.name ?? '',
      VehicleModel: product.productClass?.vehicleModels.map(
        (vm) => vm.vehicleModel.name
      ) ?? [],
    }));

  } catch (error) {
    logger.error('Failed to get featured products', error as Error, { context: 'getFeaturedProducts', limit });
    return [];
  }
}

export async function getProductById(productId: string): Promise<ProductWithDetails | null> {
  // 1. Add input validation as a good practice
  if (!productId) {
    logger.warn('[getProductById] Called with an empty or null productId.');
    return null;
  }

  const cacheKey = `product:${productId}`;

  try {
    const product = await getCachedData(
      cacheKey,
      async () => {
        // The DB call is now inside the try...catch block
        return withRetry(() =>
          prisma.product.findUnique({
            where: {
              Material_Number: productId,
            },
            include: {
              attributes: true,
              productClass: true,
            },
          })
        );
      },
      60 * 15 // 15 minutes TTL
    );

    if(!product){
      logger.warn(`[getProductById] Not found product for ${productId}`)
      return null
    }
    
    return product as ProductWithDetails;

  } catch (error) {
    logger.error(`Failed to get product by ID: ${productId}`, error as Error, {
      context: 'getProductById',
      productId: productId,
    });
    return null;
  }
}

// Search products (no caching for search - results vary too much)
export async function searchProducts(query: string, limit = 20) {
  if (!query || query.length < 2) return []
  
  return withRetry( () =>  prisma.product.findMany({
    where: {
      OR: [
        { Material_Number_normalized: { contains: query.toLowerCase() } },
        { Description_Local_normalized: { contains: query.toLowerCase() } }
      ],
      isActive: true
    },
    take: limit,
    select: {
      Material_Number: true,
      Description_Local: true,
      ImageUrl: true,
      FinalPrice: true
    }
  }))
}

// Get products by category with Redis caching
export async function getProductsByCategory(categoryId: string, page = 1, limit = 20) {
  const skip = (page - 1) * limit
  const cacheKey = `category:${categoryId}:products:${page}:${limit}`
  
  return getCachedData(
    cacheKey,
    async () => {
      const [products, total] = await Promise.all([
        withRetry( () => prisma.product.findMany({
          where: {
            categoryLevel3Id: categoryId,
            isActive: true
          },
          skip,
          take: limit,
          select: {
            Material_Number: true,
            Description_Local: true,
            ImageUrl: true,
            FinalPrice: true,
            PretAM: true,
            HasDiscount: true,
            discountPercentage: true
          },
          orderBy: {
            FinalPrice: 'asc'
          }
        })),
        withRetry( () => prisma.product.count({
          where: {
            categoryLevel3Id: categoryId,
            isActive: true
          }
        }))
      ])
      
      return {
        products,
        pagination: {
          total,
          pages: Math.ceil(total / limit),
          currentPage: page
        }
      }
    },
    60 * 5 // 5 minutes TTL
  )
}
