"use client";

import { createContext, useContext, useState, ReactNode, useCallback } from "react";

interface WishlistContextType {
  wishlistCount: number;
  setWishlistCount: React.Dispatch<React.SetStateAction<number>>;
  wishlistItems: Map<string, boolean>;
  addToWishlistOptimistic: (productCode: string) => void;
  removeFromWishlistOptimistic: (productCode: string) => void;
  revertWishlistChange: (productCode: string, previousState: boolean) => void;
  updateWishlistItem: (productCode: string, isInWishlist: boolean) => void;
}

export const WishlistContext = createContext<WishlistContextType | undefined>(undefined);

export function WishlistProvider({
  children,
  initialCount = 0,
  initialItems = new Map(),
}: {
  children: ReactNode;
  initialCount?: number;
  initialItems?: Map<string, boolean>;
}) {
  const [wishlistCount, setWishlistCount] = useState<number>(initialCount);
  const [wishlistItems, setWishlistItems] = useState<Map<string, boolean>>(initialItems);

  const addToWishlistOptimistic = useCallback((productCode: string) => {
    setWishlistItems(prev => {
      const newMap = new Map(prev);
      const wasInWishlist = newMap.get(productCode) || false;

      if (!wasInWishlist) {
        newMap.set(productCode, true);
        setWishlistCount(count => count + 1);
      }
      return newMap;
    });
  }, []);

  const removeFromWishlistOptimistic = useCallback((productCode: string) => {
    setWishlistItems(prev => {
      const newMap = new Map(prev);
      const wasInWishlist = newMap.get(productCode) || false;

      if (wasInWishlist) {
        newMap.set(productCode, false);
        setWishlistCount(count => Math.max(0, count - 1));
      }
      return newMap;
    });
  }, []);

  const revertWishlistChange = useCallback((productCode: string, previousState: boolean) => {
    setWishlistItems(prev => {
      const newMap = new Map(prev);
      const currentState = newMap.get(productCode) || false;

      if (currentState !== previousState) {
        newMap.set(productCode, previousState);
        if (previousState && !currentState) {
          // Was in wishlist, now not - increment count
          setWishlistCount(count => count + 1);
        } else if (!previousState && currentState) {
          // Was not in wishlist, now is - decrement count
          setWishlistCount(count => Math.max(0, count - 1));
        }
      }
      return newMap;
    });
  }, []);

  const updateWishlistItem = useCallback((productCode: string, isInWishlist: boolean) => {
    setWishlistItems(prev => {
      const newMap = new Map(prev);
      // Only update the item state, don't change count (optimistic functions handle count)
      newMap.set(productCode, isInWishlist);
      return newMap;
    });
  }, []);

  return (
    <WishlistContext.Provider value={{
      wishlistCount,
      setWishlistCount,
      wishlistItems,
      addToWishlistOptimistic,
      removeFromWishlistOptimistic,
      revertWishlistChange,
      updateWishlistItem
    }}>
      {children}
    </WishlistContext.Provider>
  );
}

export function useWishlist(): WishlistContextType {
  const context = useContext(WishlistContext);
  if (!context) {
    throw new Error("useWishlist must be used within a WishlistProvider");
  }
  return context;
}