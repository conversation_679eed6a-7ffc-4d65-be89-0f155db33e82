"use client";

import { createContext, useContext, useState, ReactNode } from "react";

interface WishlistContextType {
  wishlistCount: number;
  setWishlistCount: React.Dispatch<React.SetStateAction<number>>;
}

export const WishlistContext = createContext<WishlistContextType | undefined>(undefined);

export function WishlistProvider({
  children,
  initialCount = 0,
}: {
  children: ReactNode;
  initialCount?: number;
}) {
  const [wishlistCount, setWishlistCount] = useState<number>(initialCount);

  return (
    <WishlistContext.Provider value={{ wishlistCount, setWishlistCount }}>
      {children}
    </WishlistContext.Provider>
  );
}

export function useWishlist(): WishlistContextType {
  const context = useContext(WishlistContext);
  if (!context) {
    throw new Error("useWishlist must be used within a WishlistProvider");
  }
  return context;
}