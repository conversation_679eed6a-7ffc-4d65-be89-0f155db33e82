

import { WishlistItems } from "@/types/wishlist";
import WishlistProduct from "./WishlistProduct";

export default async function WishlistPage({wishlistItems}: {wishlistItems: WishlistItems[]}){

  return (
      <div>
        <h1 className="text-2xl font-semibold my-4">
         Favorite ({wishlistItems.length} {wishlistItems.length === 1 ? "produs" : "produse"})
        </h1>

        <div className="border dark:border-gray-700 rounded-lg shadow">
          {wishlistItems.map((item, key) => (
            <WishlistProduct item={item} key={key} />
          ))}
        </div>
      </div>
  );
};
