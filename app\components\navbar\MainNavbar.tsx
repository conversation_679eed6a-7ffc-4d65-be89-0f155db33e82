"server-only"

import { CartComponent } from "./Cart";
import { LogoComponent } from "./Logo";
import SearchSection from "./SearchSection";
import { UserDropdown } from "./UserDropdown";
import { DarkModeButton } from "./DarkModeButton";
import { getCurrentDbUser } from "@/lib/auth";
import { WishlistComponent } from "../wishlist/WishlistCount";
import { getCartItems } from "@/app/getData/cart";
import { CartItem } from "@/types/cart";


export async function MainNavbar({ searchParams } : { searchParams: { query: string } }){

    const user = await getCurrentDbUser();
    let cartItems: CartItem[] = [];
    
    if(!user){
      return null
    }

    cartItems = await getCartItems(user.id);
    console.log("cartItems", user.id);

    const query = searchParams.query ? searchParams.query : undefined

    return(
        <div className="flex items-center justify-between px-6 py-4">

          {/* Logo Section */}
          <LogoComponent />

          {/* Search Section */}
          <div className="flex-1 mx-8">
            <SearchSection query={query} />
          </div>

          {/* Wishlist, Cart, Account */}
          <div className="flex items-center space-x-4">

            {/* Dark Mode Button */}
            <DarkModeButton />
            
            {/* Wishlist */}
            <WishlistComponent />

            {/* Cart */}
            <CartComponent cartItems={cartItems} />

            {/* Account */} 
            <div className="flex justify-end items-center p-4 gap-4 h-16">
              <UserDropdown />
            </div>
            
          </div>
        </div>
    )
}
