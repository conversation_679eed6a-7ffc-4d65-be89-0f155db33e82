import { z } from 'zod'; // For input validation

export const FamilyCodeSchema = z.object({
    familyCode: z.
        string().
        min(4, { message: 'Family code must be at least 4 characters long' }).
        max(10, { message: 'Family code must be at most 10 characters long' })
});


export const userIdStringSchema = z.string().min(24);
export const productCodSchema = z.string().min(8).max(17)
export const cuidSchema =   z.string().cuid()
export const userIdClerkSchema =  z.string().startsWith('user_')





