"use client"

import React, { useState, useRef, useEffect } from "react";

import MegaMenu from "./MegaMenu";

interface MegaMenuProps {
  onCartClick?: () => void;
  onWishlistClick?: () => void;
  onAccountClick?: () => void;
}

const MegaMenuAndCategory: React.FC<MegaMenuProps> = ({
  onCartClick = () => {},
  onWishlistClick = () => {},
  onAccountClick = () => {},
}) => {
  const [isMegaMenuOpen, setIsMegaMenuOpen] = useState(false);
  const [activeCategory, setActiveCategory] = useState("");
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (!isMegaMenuOpen) return;

      const target = e.target as HTMLElement;
      const isNavButton = !!target.closest("nav button");
      const isInsideMegaMenu =
        menuRef.current && menuRef.current.contains(target);

      if (!isNavButton && !isInsideMegaMenu) {
        setIsMegaMenuOpen(false);
      }
    };

    document.addEventListener("click", handleClickOutside);

    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, [isMegaMenuOpen]);



  return (
    <>
      {/* Mega Menu */}
      <nav className="border-t dark:border-gray-700">
        <div
          className="flex justify-center py-3 px-4 overflow-x-auto whitespace-nowrap"
          onMouseLeave={() =>
            !menuRef.current?.matches(":hover") && setIsMegaMenuOpen(false)
          }
        >
          {[
            { name: "Lifestyle", id: "lifestyle" },
            { name: "Accesorii Interior", id: "accesorii-interior" },
            { name: "Accesorii Exterior", id: "accesorii-exterior" },
            { name: "Universale", id: "universale" },
            { name: "Roti si Anvelope", id: "wheels-tires" },
            { name: "Consumabile", id: "consumabile" },
            { name: "Piese", id: "piese" },
          ].map((item, index) => (
            <div key={item.id} className="relative">
              <button
                className={`text-sm font-medium px-4 py-3 ${
                  activeCategory === item.id 
                    ? "text-[#0066B1] border-b-2 border-[#0066B1]" 
                    : "hover:text-[#0066B1] dark:text-gray-200 dark:hover:text-[#0066B1]"
                }`}
                onClick={() => {
                  setIsMegaMenuOpen(
                    !isMegaMenuOpen || activeCategory !== item.id,
                  );
                  setActiveCategory(item.id);
                }}
                onMouseEnter={() => {
                  setActiveCategory(item.id);
                  if (!isMegaMenuOpen) {
                    setIsMegaMenuOpen(true);
                  }
                }}
              >
                {item.name}
              </button>
            </div>
          ))}
        </div>
      </nav>

      {/* Full-width mega menu container */}
      {isMegaMenuOpen && (
        <div className="absolute left-0 w-full bg-white dark:bg-gray-900 shadow-lg z-50 border-t border-gray-200 dark:border-gray-700">
          <div 
            className="max-w-7xl mx-auto"
            ref={menuRef}
            onMouseLeave={() => setIsMegaMenuOpen(false)}
          >
            <MegaMenu
              isOpen={isMegaMenuOpen}
              onClose={() => setIsMegaMenuOpen(false)}
              defaultTab={activeCategory}
            />
          </div>
        </div>
      )}
    </>
  );
};

export default MegaMenuAndCategory;

//MERGE
// "use client"

// import React, { useState } from "react";
// import {
//   ShoppingCart,
//   Heart,
//   User,
//   Truck,
//   CreditCard,
//   RefreshCw,
//   LogOut,
//   Wrench,
// } from "lucide-react";
// import { Button } from "@/components/ui/button";
// import { Badge } from "@/components/ui/badge";
// import MegaMenu from "./MegaMenu";
// import Link from "next/link";
// import SearchSection from "./SearchSection";
// import CartPreview from "./CartPreview";
// import Image from "next/image";
// import logo from "@/public/LogoBMW2.png"

// const TopNavigation = ({
// }) => {
//   const [isMegaMenuOpen, setIsMegaMenuOpen] = useState(false);
//   const [activeCategory, setActiveCategory] = useState("");

//   const cartItemCount = 3
//   const wishlistItemCount = 3

//   const cartItems = [
//     {
//       id: 1,
//       name: "M Sport Brake Kit",
//       price: 2499.99,
//       quantity: 1,
//       image:
//         "https://images.unsplash.com/photo-1615906655593-ad0386982a0f?w=400&h=300&fit=crop",
//     },
//     {
//       id: 2,
//       name: "KW V3 Coilover Kit",
//       price: 2199.99,
//       quantity: 1,
//       image:
//         "https://images.unsplash.com/photo-1619405399517-d7fce0f13302?w=400&h=300&fit=crop",
//     },
//   ];

//   return (
//     <div className="relative w-full bg-white shadow-md">
//       <div className="max-w-7xl mx-auto">
//         {/* Main Navigation Bar */}
//         <div className="flex items-center justify-between px-6 py-4">
//           {/* Logo Section */}
//           <div className="flex-shrink-0">
//             <Link href="/">
//               <div className=' flex items-center '>
//                 <Image
//                     src={logo}
//                     alt="BMW Logo"
//                     width={52}
//                     height={52}
//                     className="w-auto"
//                     priority
//                 /> 
//                 <span className='hidden md:block md:text-md lg:text-xl font-extrabold text-gray-700 whitespace-nowrap'>Automobile Bavaria</span> 
//               </div>
//             </Link>
//           </div>

//           {/* Search Section */}
//           <div className="flex-1 mx-8">
//             <SearchSection />
//           </div>

//           {/* User Controls */}
//           <div className="flex items-center space-x-4">
//             <Button
//               variant="ghost"
//               size="icon"
//               className="relative"
//             ><Link href="/account/wishlist">
//                 <Heart className="h-6 w-6 text-[#4D4D4D]" />
//                 {wishlistItemCount > 0 && (
//                   <Badge
//                     className="absolute -top-2 -right-2 bg-[#0066B1]"
//                     variant="secondary"
//                   >
//                     {wishlistItemCount}
//                   </Badge>
//                 )}
//               </Link>
//             </Button>

//             <div className="relative group">
//               <Button
//                 variant="ghost"
//                 size="icon"
//                 className="relative"
//                 onClick={() => (window.location.href = "/cart")}
//               >
//                 <ShoppingCart className="h-6 w-6 text-[#4D4D4D]" />
//                 {cartItemCount > 0 && (
//                   <Badge
//                     className="absolute -top-2 -right-2 bg-[#0066B1]"
//                     variant="secondary"
//                   >
//                     {cartItemCount}
//                   </Badge>
//                 )}
//               </Button>
//               <div className="hidden group-hover:block z-50">
//                 <CartPreview items={cartItems} />
//               </div>
//             </div>

//             <div className="relative group">
//               <Button
//                 variant="ghost"
//                 size="icon"
//                 className="group-hover:bg-gray-100"
//               >
//                 <Link href="/account/orders">
//                   <User className="h-6 w-6 text-[#4D4D4D] group-hover:text-[#0066B1]" />
//                 </Link>
//               </Button>
//               <div className="absolute right-0 mt-2 w-64 py-3 bg-white rounded-lg shadow-xl border border-gray-100 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 transform group-hover:translate-y-0 translate-y-2 divide-y divide-gray-100 z-[100]">
//                 <div className="px-4 py-2">
//                   <p className="text-sm font-medium text-gray-900">John Doe</p>
//                   <p className="text-xs text-gray-500"><EMAIL></p>
//                 </div>
//                 <div className="py-2">
//                   <Link
//                     href="/account/orders"
//                     className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2 group/item"
//                   >
//                     <ShoppingCart className="w-4 h-4 text-gray-400 group-hover/item:text-[#0066B1]" />
//                     Orders
//                   </Link>
//                   <Link
//                     href="/account/wishlist"
//                     className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2 group/item"
//                   >
//                     <Heart className="w-4 h-4 text-gray-400 group-hover/item:text-[#0066B1]" />
//                     Wishlist
//                   </Link>
//                   <Link
//                     href="/account/settings"
//                     className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2 group/item"
//                   >
//                     <User className="w-4 h-4 text-gray-400 group-hover/item:text-[#0066B1]" />
//                     Account Settings
//                   </Link>
//                   <Link
//                     href="/account/shipping"
//                     className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2 group/item"
//                   >
//                     <Truck className="w-4 h-4 text-gray-400 group-hover/item:text-[#0066B1]" />
//                     Shipping Addresses
//                   </Link>
//                   <Link
//                     href="/account/billing"
//                     className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2 group/item"
//                   >
//                     <CreditCard className="w-4 h-4 text-gray-400 group-hover/item:text-[#0066B1]" />
//                     Billing
//                   </Link>
//                   <Link
//                     href="/account/returns"
//                     className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2 group/item"
//                   >
//                     <RefreshCw className="w-4 h-4 text-gray-400 group-hover/item:text-[#0066B1]" />
//                     Returns
//                   </Link>
//                   <Link
//                     href="/account/service"
//                     className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2 group/item"
//                   >
//                     <Wrench className="w-4 h-4 text-gray-400 group-hover/item:text-[#0066B1]" />
//                     Service & Repairs
//                   </Link>
//                 </div>
//                 <div className="py-2">
//                   <button className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center gap-2 group/item">
//                     <LogOut className="w-4 h-4 text-red-400 group-hover/item:text-red-600" />
//                     Sign Out
//                   </button>
//                 </div>
//               </div>
//             </div>
//           </div>
//         </div>

//         {/* Category Navigation */}
//         <nav className="border-t border-gray-200">
//           <div className="flex justify-center gap-4 py-3 px-4 overflow-x-auto whitespace-nowrap">
//             {[
//               { name: "Lifestyle", id: "lifestyle" },
//               { name: "Roti", id: "wheels-tires" },
//               { name: "Accesorii", id: "accesorii" },
//               { name: "Consumabile", id: "consumabile" },
//               { name: "Universale", id: "universale" },
//               { name: "Caroserie", id: "caroserie" },
//               { name: "Reparatii Generale", id: "generale" },
//               { name: "Electronice", id: "electronice" },
//             ].map((item) => (
//               <Button
//                 key={item.id}
//                 variant="ghost"
//                 className="text-[#4D4D4D] hover:text-[#0066B1]"
//                 onMouseEnter={() => {
//                   setIsMegaMenuOpen(true);
//                   setActiveCategory(item.id);
//                 }}
//               >
//                 {item.name}
//               </Button>
//             ))}
//           </div>
//         </nav>

//         {/* Mega Menu */}
//         <div
//           onMouseEnter={() => setIsMegaMenuOpen(true)}
//           onMouseLeave={() => setIsMegaMenuOpen(false)}
//         >
//           <MegaMenu
//             isOpen={isMegaMenuOpen}
//             onClose={() => setIsMegaMenuOpen(false)}
//             defaultTab={activeCategory}
//           />
//         </div>
//       </div>
//     </div>
//   );
// };

// export default TopNavigation;
