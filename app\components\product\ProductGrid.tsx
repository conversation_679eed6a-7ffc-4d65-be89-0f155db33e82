"use client";

import React, { useRef, useState, useEffect } from "react";
import { motion } from "framer-motion";
import { ProductCard } from "./ProductCard";
import { ProductCardInterface } from "@/types/product";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";


const ProductGrid = ({ products, title, description }: { products: ProductCardInterface[], title: string, description: string}) => {
  return (
    <section className="py-16 w-full">
      <div className="max-w-[1640px] mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="mb-12"
        >
          <h2 className="text-3xl font-bold  mb-4">
            {title}
          </h2>
          <p className="text-lg">{description}</p>
        </motion.div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4">
          {products.map((product, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <ProductCard index={index} product={product} />
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ProductGrid;

// gridul initial cu framer
  // return (
  //   <section className="py-16 bg-white">
  //     <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
  //       <motion.div
  //         initial={{ opacity: 0, y: 20 }}
  //         whileInView={{ opacity: 1, y: 0 }}
  //         viewport={{ once: true }}
  //         transition={{ duration: 0.5 }}
  //         className="text-center mb-12"
  //       >
  //         <h2 className="text-3xl font-bold text-gray-900 mb-4">
  //           {title}
  //         </h2>
  //         <p className="text-lg text-gray-600">{description}</p>
  //       </motion.div>

  //       <div className="relative group">
  //         <div
  //           ref={scrollContainerRef}
  //           className="flex gap-6 overflow-x-auto snap-x snap-mandatory scrollbar-hide pb-4"
  //         >
  //           {products.map((product, index) => (
  //               <ProductCard key={index} index={index} product={product} />
  //           ))}
  //         </div>

  //         {/* Navigation Buttons */}
  //         <div className="absolute left-0 top-0 bottom-4 w-16 bg-gradient-to-r from-white via-white/50 to-transparent z-10" />
  //         <div className="absolute right-0 top-0 bottom-4 w-16 bg-gradient-to-l from-white via-white/50 to-transparent z-10" />
  //         <Button
  //           onClick={() => scroll("left")}
  //           className="absolute left-4 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-white/90 shadow-lg flex items-center justify-center hover:bg-white hover:scale-110 transition-all duration-300 z-20 border border-gray-200"
  //         >
  //           <ChevronLeft className="w-5 h-5 text-gray-600" />
  //         </Button>
  //         <Button
  //           onClick={() => scroll("right")}
  //           className="absolute right-4 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-white/90 shadow-lg flex items-center justify-center hover:bg-white hover:scale-110 transition-all duration-300 z-20 border border-gray-200"
  //         >
  //           <ChevronRight className="w-5 h-5 text-gray-600" />
  //         </Button>
  //       </div>
  //     </div>
  //   </section>
  // );
