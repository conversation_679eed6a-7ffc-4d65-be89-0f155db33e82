import { productCodSchema } from "@/lib/zod";
import { getProductByMaterialNumber } from "../../getData/products";
import { getStockForProduct } from "../../getData/stock";
import ProductPageContent from "@/app/components/product/ProductPage";

export default async function ProductRoute({ params }: { params: Promise<{ material_number: string }> }) {
  const { material_number } = await params;

  const product = await getProductByMaterialNumber(material_number);
  console.log(product);
    if (!product) {
    return <div>Product not found</div>;
  }
  
  const stock = await getStockForProduct(material_number);

  return (
    <div>
          {/* <ProductPageContent product={product} stock={stock} /> */}
    </div>
  );
}