"server-only"

import WishlistPage from "@/app/components/wishlist/WishlistPage"
import { getWishlistItems } from "@/app/getData/wishlist"
import { Decimal } from "@/generated/prisma/runtime/library";
import { getCurrentDbUser } from "@/lib/auth"

export interface WishlistItems {
  product: {
        Material_Number: string;
        Description_Local: string | null;
        ImageUrl: string[];
        FinalPrice:  Decimal | null;

    },
    id: string;
    productCode: string;
    userId: string;
    createdAt: Date;
    updatedAt: Date;
}

export default async function WishlistRoute() {

  const user = await getCurrentDbUser()

  if(!user){
    return null
  }

  const wishlistItems = await getWishlistItems(user.id)

  return (
  <div className="max-w-[1640px] mx-auto px-4 sm:px-6 lg:px-8">
    <WishlistPage  wishlistItems={wishlistItems} />
  </div>
)}
