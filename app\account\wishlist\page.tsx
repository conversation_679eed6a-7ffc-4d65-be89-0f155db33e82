"server-only"

import WishlistPage from "@/app/components/wishlist/WishlistPage"
import { getWishlistItems } from "@/app/getData/wishlist"
import { Decimal } from "@/generated/prisma/runtime/library";
import { getCurrentDbUser } from "@/lib/auth"
import { WishlistItems } from "@/types/wishlist";



export default async function WishlistRoute() {

  const user = await getCurrentDbUser()

  if(!user){
    return null
  }

  const wishlistItems: WishlistItems[] = await getWishlistItems(user.id)

  return (
  <div className="max-w-[1640px] mx-auto px-4 sm:px-6 lg:px-8">
    <WishlistPage  wishlistItems={wishlistItems} />
  </div>
)}
