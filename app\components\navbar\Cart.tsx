"use client"

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ShoppingCart } from "lucide-react";
import CartPreview from "./CartPreview";
import Link from "next/link";
import { useCart } from "@/app/context/CartContext";
import { CartItem } from "@/types/cart";

export function CartComponent({ cartItems }: { cartItems: CartItem[] }){
    const { getCartCount } = useCart();
    const cartCount = getCartCount();
    
    // Add a null check to prevent the error
    const safeCartItems = cartItems || [];

    return(
      <div className="relative group">
        <Link href="/cart">
          <Button
            variant="ghost"
            size="icon"
            className="relative hover:bg-gray-100 dark:hover:bg-gray-800"
          >
            <ShoppingCart className="h-6 w-6 text-[#4D4D4D] dark:text-gray-300" />
              <Badge
                className="absolute -top-2 -right-2 bg-[#0066B1]"
                variant="secondary"
              >
                {cartCount}
              </Badge>
          </Button>
        </Link>
        <div className="hidden group-hover:block">
          <CartPreview items={safeCartItems} />
        </div>
      </div>
    )
}
