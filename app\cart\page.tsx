import { getCurrentDbUser } from "@/lib/auth";
import CartPage from "../components/cart/CartPage";
import { CartItem } from "@/types/cart";
import { getCartItems } from "../getData/cart";
import { ShoppingBag, ShoppingCart } from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";

export default async function CartRoute() {
    const user = await getCurrentDbUser()

    if(!user) return null

    const cartItems: CartItem[] = await getCartItems(user.id)

    if (cartItems.length === 0) return (
        // <div className="flex min-h-[400px] flex-col items-center justify-center rounded-lg border border-dashed p-8 text-center">
        <div className="flex max-w-[1640px] mx-auto p-4 sm:p-6 lg:p-8 flex-col items-center justify-center rounded-lg border border-dashed text-center">
            <div className="flex h-20 w-20 items-center justify-center rounded-full bg-primary/10">
                <ShoppingCart className="w-10 h-10 text-primary" />
            </div>
            <h2 className="mt-6 text-xl font-semibold">Cosul tau este gol</h2>
            <p className="mb-8 mt-2 text-center text-sm leading-6 text-muted-foreground max-w-sm mx-auto">
                Nu ai adaugat inca niciun produs in cos. Exploreaza produsele noastre si adauga-le in cos.
            </p>
            <Button asChild>
                <Link href="/">Vezi produsele</Link>
            </Button>
        </div>
    )

    return (
        <CartPage cartItems={cartItems} />
    )
}
