import { getCurrentDbUser } from "@/lib/auth";
import CartPage from "../components/cart/CartPage";
import { CartItem } from "@/types/cart";
import { getCartItems } from "../getData/cart";

export default async function CartRoute() {
    const user = await getCurrentDbUser()

    if(!user) return null

    const cartItems: CartItem[] = await getCartItems(user.id)
    console.log("cartItems:", cartItems)
    return (
        <CartPage cartItems={cartItems} />
    )
}
