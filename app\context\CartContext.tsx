"use client";

import { createContext, useContext, useState, ReactNode, useOptimistic } from "react";
import { CartItem } from "@/types/cart";

interface CartContextType {
  cartItems: CartItem[];
  optimisticCartItems: CartItem[];
  addToCartOptimistic: (item: CartItem) => void;
  updateCartItemOptimistic: (itemId: string, updates: Partial<CartItem>) => void;
  removeFromCartOptimistic: (itemId: string) => void;
  revertOptimisticUpdate: (items: CartItem[]) => void;
  updateActualCart: (items: CartItem[]) => void;
  getCartCount: () => number;
  getCartItem: (itemId: string) => CartItem | undefined;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export function CartProvider({
  children,
  initialItems = []
}: {
  children: ReactNode;
  initialItems?: CartItem[];
}) {
  const [cartItems, setCartItems] = useState<CartItem[]>(initialItems);

  const [optimisticCartItems, setOptimisticCartItems] = useOptimistic(
    cartItems,
    (currentItems, action: {
      type: 'add' | 'update' | 'remove' | 'revert',
      item?: CartItem,
      itemId?: string,
      updates?: Partial<CartItem>,
      revertTo?: CartItem[]
    }) => {
      switch (action.type) {
        case 'add':
          if (!action.item) return currentItems;
          // Use Material_Number to find existing items, not id (for optimistic updates)
          const existingIndex = currentItems.findIndex(item => item.Material_Number === action.item!.Material_Number);
          if (existingIndex >= 0) {
            // Update existing item quantity
            const newItems = [...currentItems];
            newItems[existingIndex] = {
              ...newItems[existingIndex],
              quantity: newItems[existingIndex].quantity + action.item!.quantity
            };
            return newItems;
          } else {
            // Add new item
            return [...currentItems, action.item];
          }
        case 'update':
          if (!action.itemId || !action.updates) return currentItems;
          return currentItems.map(item =>
            item.id === action.itemId
              ? { ...item, ...action.updates }
              : item
          ).filter(item => item.quantity > 0); // Remove items with 0 quantity
        case 'remove':
          if (!action.itemId) return currentItems;
          return currentItems.filter(item => item.id !== action.itemId);
        case 'revert':
          return action.revertTo || currentItems;
        default:
          return currentItems;
      }
    }
  );

  const addToCartOptimistic = (item: CartItem) => {
    setOptimisticCartItems({ type: 'add', item });
  };

  const updateCartItemOptimistic = (itemId: string, updates: Partial<CartItem>) => {
    setOptimisticCartItems({ type: 'update', itemId, updates });
  };

  const removeFromCartOptimistic = (itemId: string) => {
    setOptimisticCartItems({ type: 'remove', itemId });
  };

  const revertOptimisticUpdate = (items: CartItem[]) => {
    setOptimisticCartItems({ type: 'revert', revertTo: items });
  };

  const updateActualCart = (items: CartItem[]) => {
    setCartItems(items);
  };

  const getCartCount = () => {
    return optimisticCartItems.reduce((total, item) => total + item.quantity, 0);
  };

  const getCartItem = (itemId: string) => {
    return optimisticCartItems.find(item => item.id === itemId);
  };

  const contextValue: CartContextType = {
    cartItems,
    optimisticCartItems,
    addToCartOptimistic,
    updateCartItemOptimistic,
    removeFromCartOptimistic,
    revertOptimisticUpdate,
    updateActualCart,
    getCartCount,
    getCartItem
  };

  return (
    <CartContext.Provider value={contextValue}>
      {children}
    </CartContext.Provider>
  );
}

export function useCart() {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
}