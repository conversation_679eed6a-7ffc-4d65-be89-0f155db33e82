import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { formatPriceRON } from "@/lib/utils";
import { CartItem } from "@/types/cart";
import { Minus, Plus, Trash } from "lucide-react";
import CartPageItem from "./CartPageItem";


const CartPage = ({ cartItems }: { cartItems: CartItem[] }) => {
  const subtotal = cartItems.reduce(
    (acc, item) => acc + item.FinalPrice * item.quantity,
    0,
  );
  const shipping = 0; // Free shipping
  const total = subtotal + shipping;

  //console.log("cartItems from CartPage", cartItems)

  return (
    <div className="min-h-screen py-12">
      <div className="max-w-[1640px] mx-auto px-4 sm:px-6 lg:px-8">
        <h1 className="text-2xl font-semibold mb-8">
          Cosul tau ({cartItems.length} produse)
        </h1>

        <div className="grid grid-cols-1 lg:grid-cols-[1fr_400px] gap-8">
          {/* Cart Items */}
          <div className="border rounded-lg shadow">
            {cartItems.map((item) => (
              <div
                key={item.id}
                className="flex items-center gap-6 p-6 border-b border-gray-200 last:border-0"
              >
                {/* Item Details*/}
                <CartPageItem item={item} />

                {/* Item Price */}
                <div className="text-right">
                  <p className="text-lg font-semibold">
                    {formatPriceRON(item.FinalPrice * item.quantity)}
                  </p>
                  <p className="text-sm text-gray-500 mt-1">
                    {formatPriceRON(item.FinalPrice)} buc
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* Order Summary */}
          <div className="border rounded-lg shadow p-6 h-fit">
            <h2 className="text-xl font-semibold mb-6">
              Sumar comanda
            </h2>

            <div className="space-y-4">
              <div className="flex justify-between">
                <span>Subtotal</span>
                <span>{ formatPriceRON(subtotal) }</span>
              </div>
              <div className="flex justify-between">
                <span>Taxa de livrare</span>
                <span>
                  {shipping > 0 ? "Free" : `${formatPriceRON(shipping)}`}
                </span>
              </div>
              <div className="border-t border-gray-200 pt-4">
                <div className="flex justify-between font-semibold text-lg">
                  <span>Total</span>
                  <span>{formatPriceRON(total)}</span>
                </div>
              </div>
            </div>

            <Button
              className="w-full mt-6 bg-[#0066B1] hover:bg-[#004d85]"
            >
              Lanseaza comanda
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CartPage;
