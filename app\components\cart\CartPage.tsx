"use client"

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { formatPriceRON } from "@/lib/utils";
import { CartItem } from "@/types/cart";
import { Minus, Plus, Trash } from "lucide-react";


const CartPage = ({ cartItems }: { cartItems: CartItem[] }) => {
  const subtotal = cartItems.reduce(
    (acc, item) => acc + item.FinalPrice * item.quantity,
    0,
  );
  const shipping = 0; // Free shipping
  const total = subtotal + shipping;

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h1 className="text-2xl font-semibold mb-8">
          Cosul tau ({cartItems.length} produse)
        </h1>

        <div className="grid grid-cols-1 lg:grid-cols-[1fr_400px] gap-8">
          {/* Cart Items */}
          <div className="bg-white rounded-lg shadow">
            {cartItems.map((item) => (
              <div
                key={item.id}
                className="flex items-center gap-6 p-6 border-b border-gray-200 last:border-0"
              >
                <div className="w-24 h-24 rounded-lg overflow-hidden">
                  <img
                    src={item.ImageUrl[0]}
                    alt={item.Description_Local}
                    className="w-full h-full object-cover"
                  />
                </div>

                <div className="flex-1">
                  <h3 className="text-lg font-medium">{item.Description_Local}</h3>
                  <p className="text-sm text-gray-500 mt-1">
                    OE: {item.Material_Number}
                  </p>
                  <div className="mt-2">
                    <Input
                      id={`vin-${item.id}`}
                      placeholder="Introdu numarul de identificare vehiculului (VIN)"
                    />
                  </div>
                  <div className="flex items-center gap-4 mt-4">
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="icon" className="h-8 w-8">
                        <Minus className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="icon" className="h-8 w-14">
                        {item.quantity}
                        </Button>
                      <Button variant="outline" size="icon" className="h-8 w-8">
                        <Plus className="w-4 h-4" />
                      </Button>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      className="gap-2 text-red-600 hover:text-red-700"
                    >
                      <Trash className="w-4 h-4" /> Sterge
                    </Button>
                  </div>
                </div>

                <div className="text-right">
                  <p className="text-lg font-semibold">
                    {formatPriceRON(item.FinalPrice * item.quantity)}
                  </p>
                  <p className="text-sm text-gray-500 mt-1">
                    {formatPriceRON(item.FinalPrice)} buc
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* Order Summary */}
          <div className="bg-white rounded-lg shadow p-6 h-fit">
            <h2 className="text-xl font-semibold mb-6">
              Sumar comanda
            </h2>

            <div className="space-y-4">
              <div className="flex justify-between">
                <span>Subtotal</span>
                <span>Ron { formatPriceRON(subtotal) }</span>
              </div>
              <div className="flex justify-between">
                <span>Taxa de livrare</span>
                <span>
                  {shipping > 0 ? "Free" : `Ron ${formatPriceRON(shipping)}`}
                </span>
              </div>
              <div className="border-t border-gray-200 pt-4">
                <div className="flex justify-between font-semibold text-lg">
                  <span>Total</span>
                  <span>Ron {formatPriceRON(total)}</span>
                </div>
              </div>
            </div>

            <Button
              className="w-full mt-6 bg-[#0066B1] hover:bg-[#004d85]"
            >
              Lanseaza comanda
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CartPage;
