
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ShoppingCart, Trash } from "lucide-react";
import { cn, formatPriceRON, getStockStatus } from "@/lib/utils";
import { getStockForProduct } from "@/app/getData/stock";
import RemoveProductWishlist from "./WishlistRemoveButton";
import { WishlistItems } from "@/types/wishlist";

export default async function WishlistProduct({item}: {item: WishlistItems}){
    const stock = await getStockForProduct(item.productCode)
    // const stockStatus = getStockStatus(stock);
    return(
        <>
        <div
              key={item.id}
              className="flex items-center gap-6 p-6 border-b border-gray-200 last:border-0"
            >
              <div className="w-24 h-24 rounded-lg overflow-hidden">
                <img
                  src={item.product.ImageUrl[0]}
                  alt={item.product.Description_Local || ""}
                  className="w-full h-full object-cover"
                />
              </div>

              <div className="flex-1">
                <h3 className="text-lg font-medium">{item.product.Description_Local || ""}</h3>
                <p className="text-sm text-gray-500 mt-1">
                  OE Code: {item.productCode}
                </p>
                {/* <p className="text-sm mt-1">
                    <span
                      className={cn(
                        "px-2 py-1 rounded-full text-xs",
                        stockStatus === "IN_STOCK"
                          ? "bg-green-100 text-green-800"
                          : stockStatus === "LOW_STOCK"
                            ? "bg-yellow-100 text-yellow-800"
                            : "bg-red-100 text-red-800",
                      )}
                    >
                      {stockStatus.replace(/_/g, " ")}
                    </span>  
                </p> */}
              </div>

              <div className="text-right">
                <p className="text-lg font-semibold">
                  {formatPriceRON(item.product.FinalPrice)}
                </p>
                <div className="flex gap-2 mt-2">
                  <Button variant="outline" size="sm" className="gap-2">
                    <ShoppingCart className="w-4 h-4" /> Add to Cart
                  </Button>
                  <RemoveProductWishlist product={item.productCode} />
                </div>
              </div>
            </div>
        </>
    )
}