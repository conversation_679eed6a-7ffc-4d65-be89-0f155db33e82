import { StockStatus } from "@/generated/prisma";

export interface ProductCardInterface {
  id: string;
  Material_Number: string;
  Description_Local: string;
  PretAM: number;
  FinalPrice?: number;
  priceRange?: string;
  ImageUrl: string;
  categoryLevel3: string; //name from CategoryLevel3
  HasDiscount?: boolean;
  activeDiscountType?: string;
  activeDiscountValue?: string;
  VehicleModel: string[]; //compatible models 
  stockStatus: StockStatus;
  isInWishlist?: boolean;
  stock?: number;
  discountPercentage?: string;
}