"server-only"

import { getCurrentDbUser } from "@/lib/auth"
import { prisma, withRetry } from "@/lib/db"
import { logger } from "@/lib/logger"
import { redis } from "@/lib/redis"
import { getCachedData } from "@/lib/redis-cache"
import { userIdClerkSchema } from "@/lib/zod"
import { Cart } from "@/types/cart"
import { cache } from "react"


export async function getUserIdFromDB(clerId: string){
  if (!clerId){
    logger.warn(`[getUserIdFromDB] No paramater provided`)
    return null
  }

  try{
    //const userIdParsed = cuidSchema.safeParse(userIdDb)
    const parse = userIdClerkSchema.safeParse(clerId)

    if (!parse.success) {
      logger.warn("[getUserIdFromDB] Invalid user ID format")
      return null
    }

    const userId = parse.data

    const userDB = await withRetry(() =>
          prisma.user.findUnique({
            where: { externalId: userId },
            select: {id: true}
          })
        );

    if(!userDB) return null

    return userDB

  }catch(e){
    logger.error(`[getUserIdFromDB] Error trying to get the wishlist count for clerId ${clerId}: ${e}`)
    return null
  }

}

// // Get recent orders for the current user
// export const getRecentOrders = (async (userId: string, limit = 8): Promise< RecentOrder[]> => {

//   if (!userId) return []
    
//   const raws : RecentOrder[] = await withRetry(() =>
//     prisma.order.findMany({
//       where: { userId },
//       orderBy: { createdAt: "desc" },
//       take: limit,
//       select: {
//         id: true,
//         createdAt: true,
//         orderItems: {
//           select: {
//             product: {
//               select: {
//                 id: true,
//                 Material_Number: true,
//                 Description_Local: true,
//                 PretAM: true,
//                 FinalPrice: true,
//                 priceRange: true,        // ← added
//                 ImageUrl: true,

//                 categoryLevel3: { select: { name: true } },

//                 HasDiscount: true,
//                 discountPercentage: true,
//                 activeDiscountType: true,
//                 activeDiscountValue: true,
                
//                 productClass: {
//                   select: {
//                     vehicleModels: {
//                       select: {
//                         vehicleModel: { select: { name: true } },
//                       },
//                     },
//                   },
//                 },
//                 wishlist: {
//                   select: {
//                     id: true,
//                   },
//                 },
//               },
//             },
//           },
//         },
//       },
//     }) 
//   )

//   if (!raws || !Array.isArray(raws)) return [];

//   return raws.map((order): RecentOrder => ({
//     id: order.id,
//     createdAt: order.createdAt,
//     items: order.orderItems.map((orderItem: unknown): RecentOrderItem => {
//       const p = orderItem.product;

//       return {
//         id: p.id,
//         Material_Number: p.Material_Number,
//         Description_Local: p.Description_Local,
//         PretAM: p.PretAM?.toNumber() ?? null,
//         FinalPrice: p.FinalPrice?.toNumber() ?? null,
//         priceRange: p.priceRange,
//         ImageUrl: p.ImageUrl,
//         categoryLevel3: p.categoryLevel3?.name ?? null,
//         HasDiscount: p.HasDiscount,
//         discountPercentage: p.discountPercentage?.toNumber() ?? null,
//         activeDiscountType: p.activeDiscountType,
//         activeDiscountValue: p.activeDiscountValue?.toNumber() ?? null,
//         vehicleModels: p.productClass
//           ? p.productClass.vehicleModels.map(vm => vm.vehicleModel.name)
//           : [],
//         stockStatus: p.stockStatus,
//         isInWishlist: false,
//       };
//     }),
//   }));
// });








