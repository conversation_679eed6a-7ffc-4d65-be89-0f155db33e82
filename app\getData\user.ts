"server-only"

import { getCurrentDbUser } from "@/lib/auth"
import { prisma, withRetry } from "@/lib/db"
import { logger } from "@/lib/logger"
import { redis } from "@/lib/redis"
import { getCachedData } from "@/lib/redis-cache"
import { userIdClerkSchema, userIdStringSchema } from "@/lib/zod"
import { Cart } from "@/types/cart"
import { ProductCardInterface } from "@/types/product"
import { cache } from "react"


export async function getUserIdFromDB(clerId: string){
  if (!clerId){
    logger.warn(`[getUserIdFromDB] No paramater provided`)
    return null
  }

  try{
    //const userIdParsed = cuidSchema.safeParse(userIdDb)
    const parse = userIdClerkSchema.safeParse(clerId)

    if (!parse.success) {
      logger.warn("[getUserIdFromDB] Invalid user ID format")
      return null
    }

    const userId = parse.data

    const userDB = await withRetry(() =>
          prisma.user.findUnique({
            where: { externalId: userId },
            select: {id: true}
          })
        );

    if(!userDB) return null

    return userDB

  }catch(e){
    logger.error(`[getUserIdFromDB] Error trying to get the wishlist count for clerId ${clerId}: ${e}`)
    return null
  }

}

// Get recent orders for the current user
export const getRecentOrders = (async (userId: string, limit = 8): Promise<ProductCardInterface[]> => {

  if (!userId) return []
    
  const orders = await withRetry(() =>
    prisma.order.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: limit,
      include: {
        orderItems: {
          include: {
            product: {
                select: {
                    id: true,
                    Material_Number: true,
                    Description_Local: true,
                    PretAM: true,
                    FinalPrice: true,
                    ImageUrl: true,
                    categoryLevel3: {
                    select: { name: true }
                    },
                    HasDiscount: true,
                    stockStatus: true,
                    productClass: {
                    select: {
                        vehicleModels: {
                        select: {
                            vehicleModel: {
                            select: {
                                name: true
                            }
                            }
                        }
                        }
                    }
                    }
                }
                },
          },
        },
      },
    })
  );

  if (!orders) return [];

  // Flatten the products from all orders
  const productMap = new Map<string, ProductCardInterface>();

  for (const order of orders) {
    for (const item of order.orderItems) {
      const p = item.product;

      if (!p) continue; // safeguard

      if (!productMap.has(p.id)) {
        productMap.set(p.id, {
          id: p.id,
          Material_Number: p.Material_Number,
          Description_Local: p.Description_Local ?? '',
          PretAM: Number(p.PretAM ?? 0),
          FinalPrice: p.FinalPrice ? Number(p.FinalPrice) : undefined,
          ImageUrl: p.ImageUrl?.[0] ?? '',
          categoryLevel3: p.categoryLevel3?.name ?? '',
          HasDiscount: p.HasDiscount ?? false,
          VehicleModel: p.productClass?.vehicleModels?.map(vm => vm.vehicleModel.name) ?? [],
          stockStatus: p.stockStatus,
        });
      }
    }
  }

  return Array.from(productMap.values());
});








