import { User } from "@/generated/prisma";
import { prisma, withRetry } from "./db";
import { logger } from "./logger";

export async function findOrCreateUser(params: {
  externalId: string;
  email: string;
  firstName?: string;
  lastName?: string;
  profileImage?: string;
  updateLoginStats?: boolean;
}): Promise<User | null> {
  const { externalId, email, firstName, lastName, profileImage, updateLoginStats } = params;

  // Input validation
  if (!externalId || !email) {
    logger.warn('[findOrCreateUser] Missing required fields: externalId or email');
    return null;
  }

  try {
    const dbUser = await withRetry(() =>
      prisma.$transaction(async (tx) => {
        // First try to find by externalId
        let user = await tx.user.findUnique({ 
          where: { externalId } 
        });

        if (user) {
          // User exists, update login stats if needed
          if (updateLoginStats) {
            user = await tx.user.update({
              where: { id: user.id },
              data: {
                lastLoginAt: new Date(),
                loginCount: { increment: 1 },
                lastActivityAt: new Date(),
              },
            });
          }
          return user;
        }

        // Check if user exists by email
        const existingUserByEmail = await tx.user.findUnique({ 
          where: { email } 
        });

        if (existingUserByEmail) {
          // Security check: Only merge if same provider or no provider
          if (!existingUserByEmail.externalProvider || existingUserByEmail.externalProvider === 'clerk') {
            console.log("findOrCreateuser function gets called")
            return await tx.user.update({
              where: { id: existingUserByEmail.id },
              data: {
                externalId,
                externalProvider: "clerk",
                firstName: firstName || existingUserByEmail.firstName,
                lastName: lastName || existingUserByEmail.lastName,
                profileImage: profileImage || existingUserByEmail.profileImage,
                ...(updateLoginStats
                  ? {
                      lastLoginAt: new Date(),
                      loginCount: { increment: 1 },
                      lastActivityAt: new Date(),
                    }
                  : {}),
              },
            });
          } else {
            logger.warn(`[findOrCreateUser] Email ${email} already exists with different provider: ${existingUserByEmail.externalProvider}`);
            //throw new Error('Email already exists with different provider');
            return null
          }
        }

        // Create new user
        return await tx.user.create({
          data: {
            externalId,
            email,
            firstName: firstName || "",
            lastName: lastName || "",
            profileImage: profileImage || "",
            externalProvider: "clerk",
            lastLoginAt: updateLoginStats ? new Date() : undefined,
            loginCount: updateLoginStats ? 1 : 0,
            lastActivityAt: updateLoginStats ? new Date() : undefined,
          },
        });
      }, {
        timeout: 10000, // Add transaction timeout
      })
    );

    return dbUser;
  } catch (error) {
    logger.error('[findOrCreateUser] Error:', error);
    
    // Don't expose internal errors to client
    if (error instanceof Error && error.message.includes('different provider')) {
      //throw error; // Re-throw security errors
      return null
    }
    
    return null;
  }
}